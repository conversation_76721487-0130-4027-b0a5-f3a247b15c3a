import asyncio
import base64
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import pandas as pd
from llama_index.core.workflow import (
    Context,
    Event,
    StartEvent,
    StopEvent,
    Workflow,
    step,
)
from openai import OpenAI

from src.log.loggers import get_logger
from src.proc_file.indexer import Query<PERSON>ontent, indexer
from src.proc_file.proc_file import FileProcessor

logger = get_logger("limit_extract_workflow")


class WorkflowInput(Event):
    """Workflow输入事件"""

    file_path: Path  # PDF文件路径
    image_path: Path  # 营养成分表图片路径
    user_id: str = "test_user"
    queries: list[str] = []  # 多个查询


class DocParseEvent(Event):
    """文档解析事件"""

    file_path: Path
    user_id: str
    queries: list[str]


class DocSearchEvent(Event):
    """文档搜索事件"""

    queries: list[str]
    user_id: str


class NTParseEvent(Event):
    """营养成分表解析事件"""

    image_path: Path


class MergeEvent(Event):
    """合并事件"""

    source: str  # "doc_search" 或 "nutrition_table"
    data: Dict[str, Any]


class ComposeResultEvent(Event):
    """结果组合事件"""

    doc_data: Dict[str, Any]
    nutrition_data: Dict[str, Any]


class ErrorEvent(Event):
    """错误事件"""

    error_type: str  # "validation", "processing", "system"
    error_message: str
    error_details: Dict[str, Any] = {}
    step_name: str = ""


class LimitExtractWorkflow(Workflow):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.file_processor = FileProcessor()
        self.client = OpenAI(
            api_key=os.environ["OPENAI_API_KEY"], base_url=os.environ["OPENAI_BASE_URL"]
        )

    def _validate_required_parameters(self, workflow_input: dict) -> None:
        """验证必需的参数"""
        required_params = {
            "file_path": "PDF文件路径",
            "image_path": "营养成分表图片路径",
            "user_id": "用户ID",
            "queries": "查询列表",
        }

        missing_params = []
        for param, description in required_params.items():
            if param not in workflow_input or workflow_input[param] is None:
                missing_params.append(f"{param} ({description})")

        if missing_params:
            error_msg = f"缺少必需参数: {', '.join(missing_params)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证参数类型和有效性
        file_path = workflow_input["file_path"]
        if not isinstance(file_path, (str, Path)):
            raise ValueError(
                f"file_path 必须是字符串或Path对象，当前类型: {type(file_path)}"
            )

        image_path = workflow_input["image_path"]
        if not isinstance(image_path, (str, Path)):
            raise ValueError(
                f"image_path 必须是字符串或Path对象，当前类型: {type(image_path)}"
            )

        user_id = workflow_input["user_id"]
        if not isinstance(user_id, str) or not user_id.strip():
            raise ValueError("user_id 必须是非空字符串")

        queries = workflow_input["queries"]
        if not isinstance(queries, list) or len(queries) == 0:
            raise ValueError("queries 必须是非空列表")

        for i, query in enumerate(queries):
            if not isinstance(query, str) or not query.strip():
                raise ValueError(f"queries[{i}] 必须是非空字符串")

    @step
    async def dispatch_query(
        self, ctx: Context, ev: StartEvent
    ) -> DocParseEvent | NTParseEvent | ErrorEvent | None:
        """分发查询到两个并行分支"""
        try:
            # 从StartEvent中获取数据
            workflow_input = ev.data if hasattr(ev, "data") and ev.data else {}

            # 验证必需参数
            self._validate_required_parameters(workflow_input)

            # 获取验证后的参数
            file_path = Path(workflow_input["file_path"])
            image_path = Path(workflow_input["image_path"])
            user_id = workflow_input["user_id"]
            queries = workflow_input["queries"]

            logger.info(f"开始处理文件: {file_path}, 图片: {image_path}")
            logger.info(f"用户ID: {user_id}")
            logger.info(f"查询项目数量: {len(queries)}")
            logger.info(f"查询项目: {queries}")

            # 发送文档解析事件
            ctx.send_event(
                DocParseEvent(file_path=file_path, user_id=user_id, queries=queries)
            )

            # 发送营养成分表解析事件
            ctx.send_event(NTParseEvent(image_path=image_path))

        except Exception as e:
            logger.error(f"参数验证或事件分发失败: {e}")
            # 发送错误事件
            ctx.send_event(
                ErrorEvent(
                    error_type="validation",
                    error_message=str(e),
                    error_details={
                        "step": "dispatch_query",
                        "input_data": workflow_input,
                    },
                    step_name="dispatch_query",
                )
            )

    @step
    async def parse_doc(
        self, ctx: Context, ev: DocParseEvent
    ) -> DocSearchEvent | ErrorEvent | None:
        """解析文档并建立索引"""
        try:
            logger.info(f"开始解析文档: {ev.file_path}")

            # 验证文档路径
            if not ev.file_path or str(ev.file_path).strip() == "":
                raise ValueError("文档路径为空")

            # 调用文件处理器处理PDF文档
            success = self.file_processor.proc_file(ev.file_path, ev.user_id)

            if not success:
                raise ValueError(f"文档处理失败: {ev.file_path}")

            logger.info("文档解析完成，开始搜索")
            return DocSearchEvent(queries=ev.queries, user_id=ev.user_id)

        except Exception as e:
            logger.error(f"文档解析失败: {e}")
            # 发送错误事件
            ctx.send_event(
                ErrorEvent(
                    error_type="processing",
                    error_message=f"文档解析失败: {str(e)}",
                    error_details={
                        "step": "parse_doc",
                        "file_path": str(ev.file_path),
                        "user_id": ev.user_id,
                        "queries_count": len(ev.queries),
                    },
                    step_name="parse_doc",
                )
            )

    @step
    async def map_reduce_search(
        self, ctx: Context, ev: DocSearchEvent
    ) -> MergeEvent | ErrorEvent | None:
        """在索引中搜索相关信息"""
        try:
            if not ev.queries:
                logger.warning("查询列表为空，发送错误事件")
                ctx.send_event(
                    ErrorEvent(
                        error_type="validation",
                        error_message="查询列表为空，无法执行搜索",
                        error_details={
                            "step": "map_reduce_search",
                            "user_id": ev.user_id,
                            "queries_count": 0,
                        },
                        step_name="map_reduce_search",
                    )
                )
                return

            logger.info(f"开始执行 {len(ev.queries)} 个查询")
            results = {}
            failed_queries = []

            for i, query in enumerate(ev.queries):
                try:
                    logger.info(f"执行查询 {i + 1}/{len(ev.queries)}: {query}")
                    query_content = QueryContent(query=query, user_id=ev.user_id)
                    result = indexer.query(query_content)
                    results[query] = result
                except Exception as e:
                    logger.error(f"查询失败 '{query}': {e}")
                    results[query] = f"查询失败: {str(e)}"
                    failed_queries.append({"query": query, "error": str(e)})

            # 如果所有查询都失败了，发送错误事件
            if len(failed_queries) == len(ev.queries):
                logger.error("所有查询都失败了")
                ctx.send_event(
                    ErrorEvent(
                        error_type="processing",
                        error_message="所有文档查询都失败了",
                        error_details={
                            "step": "map_reduce_search",
                            "user_id": ev.user_id,
                            "total_queries": len(ev.queries),
                            "failed_queries": failed_queries,
                        },
                        step_name="map_reduce_search",
                    )
                )
                return

            logger.info("文档搜索完成")
            return MergeEvent(source="doc_search", data={"results": results})

        except Exception as e:
            logger.error(f"文档搜索失败: {e}")
            # 发送错误事件
            ctx.send_event(
                ErrorEvent(
                    error_type="system",
                    error_message=f"文档搜索系统错误: {str(e)}",
                    error_details={
                        "step": "map_reduce_search",
                        "user_id": ev.user_id,
                        "queries_count": len(ev.queries) if ev.queries else 0,
                    },
                    step_name="map_reduce_search",
                )
            )

    def _img2base64(self, img_path: Path) -> str:
        """将图片转换为base64编码"""
        with open(img_path, "rb") as f:
            image_data = f.read()
            encoded_string = base64.b64encode(image_data).decode("utf-8")
        return encoded_string

    def _get_nutrition_ocr_schema(self) -> dict:
        """获取营养成分表OCR的JSON Schema"""
        return {
            "name": "nutrition_ocr_output",
            "schema": {
                "title": "OcrResult",
                "type": "object",
                "additionalProperties": False,
                "required": ["result"],
                "properties": {
                    "result": {
                        "anyOf": [
                            {
                                "title": "NutritionTable",
                                "type": "object",
                                "required": ["serving_size", "serving_unit", "items"],
                                "additionalProperties": False,
                                "properties": {
                                    "serving_size": {
                                        "description": "每份的大小，例如 100",
                                        "title": "Serving Size",
                                        "type": "number",
                                    },
                                    "serving_unit": {
                                        "description": "每份的单位，例如 g 或 ml",
                                        "title": "Serving Unit",
                                        "type": "string",
                                        "enum": ["g", "ml"],
                                    },
                                    "items": {
                                        "description": "营养成分列表",
                                        "title": "Items",
                                        "type": "array",
                                        "items": {
                                            "title": "NutritionItem",
                                            "type": "object",
                                            "required": [
                                                "name",
                                                "amount",
                                                "unit",
                                                "nrv_percent",
                                            ],
                                            "additionalProperties": False,
                                            "properties": {
                                                "name": {
                                                    "description": "营养成分名称，例如 蛋白质、脂肪 等",
                                                    "title": "Name",
                                                    "type": "string",
                                                },
                                                "amount": {
                                                    "description": "每份中的含量数值",
                                                    "title": "Amount",
                                                    "type": "number",
                                                },
                                                "unit": {
                                                    "description": "含量单位，如 g、mg、kJ 等",
                                                    "title": "Unit",
                                                    "type": "string",
                                                },
                                                "nrv_percent": {
                                                    "anyOf": [
                                                        {
                                                            "description": "营养素参考值百分比（NRV%）",
                                                            "title": "Nrv Percent",
                                                            "type": "number",
                                                        },
                                                        {
                                                            "type": "null",
                                                        },
                                                    ]
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                            {"type": "string"},
                        ],
                        "description": "识别结果, 类型为一个营养成分表或错误信息字符串",
                        "title": "Result",
                    }
                },
            },
        }

    def _llm_ocr(self, encoded_string: str, model: str = "gpt-4o-mini") -> str:
        """使用LLM进行OCR识别营养成分表"""
        ocr_prompt = """
        请你直接扮演ocr的角色，把下面的图片中的营养成分表识别出来，并按照json格式输出。
        如果无法识别，则OcrResult.result直接设置为"未检测到营养成分表"。
        """

        try:
            schema = self._get_nutrition_ocr_schema()
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": ocr_prompt},
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{encoded_string}",
                                    "detail": "high",
                                },
                            }
                        ],
                    },
                ],
                response_format={"type": "json_schema", "json_schema": schema},
            )
            result = response.choices[0].message.content
            return result if result is not None else "{}"
        except Exception as e:
            logger.error(f"LLM OCR失败: {e}")
            return json.dumps({"result": f"OCR识别失败: {str(e)}"})

    @step
    async def extract_nutrition_table(
        self, ctx: Context, ev: NTParseEvent
    ) -> MergeEvent | ErrorEvent | None:
        """提取营养成分表"""
        try:
            logger.info(f"开始提取营养成分表: {ev.image_path}")

            # 验证图片路径
            if not ev.image_path or str(ev.image_path).strip() == "":
                logger.error("图片路径为空")
                ctx.send_event(
                    ErrorEvent(
                        error_type="validation",
                        error_message="营养成分表图片路径为空",
                        error_details={
                            "step": "extract_nutrition_table",
                            "image_path": str(ev.image_path),
                        },
                        step_name="extract_nutrition_table",
                    )
                )
                return

            if not ev.image_path.exists():
                logger.error(f"图片文件不存在: {ev.image_path}")
                ctx.send_event(
                    ErrorEvent(
                        error_type="validation",
                        error_message=f"营养成分表图片文件不存在: {ev.image_path}",
                        error_details={
                            "step": "extract_nutrition_table",
                            "image_path": str(ev.image_path),
                        },
                        step_name="extract_nutrition_table",
                    )
                )
                return

            # 尝试使用LLM进行OCR识别
            try:
                # 将图片转换为base64
                encoded_string = self._img2base64(ev.image_path)

                # 使用LLM进行OCR识别
                ocr_result = self._llm_ocr(encoded_string)

                # 解析JSON结果
                try:
                    nutrition_data = json.loads(ocr_result)
                    # 检查是否是有效的营养成分数据
                    if "result" in nutrition_data and isinstance(
                        nutrition_data["result"], dict
                    ):
                        logger.info("营养成分表OCR识别成功")
                        return MergeEvent(source="nutrition_table", data=nutrition_data)
                    elif "result" in nutrition_data and isinstance(
                        nutrition_data["result"], str
                    ):
                        # OCR返回的是字符串结果，直接返回这个字符串
                        logger.info(
                            f"营养成分表OCR返回字符串结果: {nutrition_data['result']}"
                        )
                        return MergeEvent(source="nutrition_table", data=nutrition_data)
                    else:
                        # OCR返回的数据格式不正确，发送错误事件
                        error_msg = "OCR返回的数据格式不正确"
                        logger.error(f"OCR识别失败: {error_msg}")
                        ctx.send_event(
                            ErrorEvent(
                                error_type="processing",
                                error_message=f"营养成分表OCR识别失败: {error_msg}",
                                error_details={
                                    "step": "extract_nutrition_table",
                                    "image_path": str(ev.image_path),
                                    "ocr_result": nutrition_data,
                                },
                                step_name="extract_nutrition_table",
                            )
                        )
                        return

                except (json.JSONDecodeError, ValueError) as e:
                    logger.error(f"OCR结果解析失败: {e}")
                    ctx.send_event(
                        ErrorEvent(
                            error_type="processing",
                            error_message=f"营养成分表OCR结果解析失败: {str(e)}",
                            error_details={
                                "step": "extract_nutrition_table",
                                "image_path": str(ev.image_path),
                                "ocr_raw_result": ocr_result,
                                "parse_error": str(e),
                            },
                            step_name="extract_nutrition_table",
                        )
                    )
                    return

            except Exception as e:
                logger.error(f"OCR识别过程失败: {e}")
                ctx.send_event(
                    ErrorEvent(
                        error_type="processing",
                        error_message=f"营养成分表OCR识别过程失败: {str(e)}",
                        error_details={
                            "step": "extract_nutrition_table",
                            "image_path": str(ev.image_path),
                            "ocr_error": str(e),
                        },
                        step_name="extract_nutrition_table",
                    )
                )
                return

        except Exception as e:
            logger.error(f"营养成分表提取失败: {e}")
            # 发送错误事件
            ctx.send_event(
                ErrorEvent(
                    error_type="system",
                    error_message=f"营养成分表提取系统错误: {str(e)}",
                    error_details={
                        "step": "extract_nutrition_table",
                        "image_path": str(ev.image_path),
                    },
                    step_name="extract_nutrition_table",
                )
            )

    @step
    async def collect(
        self, ctx: Context, ev: MergeEvent
    ) -> ComposeResultEvent | ErrorEvent | None:
        """收集两个分支的结果"""
        try:
            # 收集两个MergeEvent
            result = ctx.collect_events(ev, [MergeEvent] * 2)
            if result is None:
                return None

            # 验证收集到的事件数量
            if len(result) != 2:
                logger.error(f"期望收集2个事件，实际收集到{len(result)}个")
                ctx.send_event(
                    ErrorEvent(
                        error_type="system",
                        error_message=f"事件收集不完整，期望2个事件，实际收集到{len(result)}个",
                        error_details={
                            "step": "collect",
                            "expected_events": 2,
                            "actual_events": len(result),
                            "event_sources": [event.source for event in result],
                        },
                        step_name="collect",
                    )
                )
                return

            # 分离文档搜索结果和营养成分表结果
            doc_data = {}
            nutrition_data = {}
            found_sources = []

            for event in result:
                found_sources.append(event.source)
                if event.source == "doc_search":
                    doc_data = event.data
                elif event.source == "nutrition_table":
                    nutrition_data = event.data

            # 验证是否收集到了所有必需的数据源
            expected_sources = {"doc_search", "nutrition_table"}
            actual_sources = set(found_sources)

            if not expected_sources.issubset(actual_sources):
                missing_sources = expected_sources - actual_sources
                logger.error(f"缺少必需的数据源: {missing_sources}")
                ctx.send_event(
                    ErrorEvent(
                        error_type="system",
                        error_message=f"缺少必需的数据源: {', '.join(missing_sources)}",
                        error_details={
                            "step": "collect",
                            "expected_sources": list(expected_sources),
                            "actual_sources": list(actual_sources),
                            "missing_sources": list(missing_sources),
                        },
                        step_name="collect",
                    )
                )
                return

            logger.info("结果收集完成")
            return ComposeResultEvent(doc_data=doc_data, nutrition_data=nutrition_data)

        except Exception as e:
            logger.error(f"结果收集失败: {e}")
            # 发送错误事件
            ctx.send_event(
                ErrorEvent(
                    error_type="system",
                    error_message=f"结果收集系统错误: {str(e)}",
                    error_details={"step": "collect"},
                    step_name="collect",
                )
            )

    def _create_document_analysis_table(self, doc_results: dict) -> dict:
        """创建文档分析结果表格"""
        if not doc_results or "results" not in doc_results:
            return {"table_type": "document_analysis", "headers": [], "rows": []}

        headers = ["查询项目", "提取结果", "结果类型"]
        rows = []

        for query, result in doc_results["results"].items():
            # 判断结果类型
            if "限量" in query or "标准" in query:
                result_type = "限值标准"
            elif "要求" in query:
                result_type = "技术要求"
            elif "保质期" in query:
                result_type = "保质期信息"
            elif "贮存" in query or "储存" in query:
                result_type = "贮存条件"
            elif "感官" in query:
                result_type = "感官指标"
            else:
                result_type = "其他信息"

            # 截断过长的结果
            display_result = result[:100] + "..." if len(result) > 100 else result

            rows.append([query, display_result, result_type])

        return {
            "table_type": "document_analysis",
            "title": "文档分析结果表",
            "headers": headers,
            "rows": rows,
            "total_queries": len(rows),
        }

    def _create_nutrition_table(self, nutrition_data: dict) -> dict:
        """创建营养成分表格"""
        if not nutrition_data or "result" not in nutrition_data:
            return {"table_type": "nutrition_facts", "headers": [], "rows": []}

        result = nutrition_data["result"]

        # 如果是字符串（错误信息），创建一个显示错误的表格
        if isinstance(result, str):
            headers = ["营养成分", "含量", "单位", "NRV%"]
            rows = [
                ["状态", "OCR识别失败", "", ""],
                [
                    "错误信息",
                    result[:50] + "..." if len(result) > 50 else result,
                    "",
                    "",
                ],
            ]
            return {
                "table_type": "nutrition_facts",
                "title": "营养成分表",
                "headers": headers,
                "rows": rows,
                "error": result,
            }

        # 创建营养成分表格
        headers = ["营养成分", "含量", "单位", "NRV%"]
        rows = []

        # 添加基本信息行
        serving_info = (
            f"每{result.get('serving_size', 100)}{result.get('serving_unit', 'g')}"
        )
        rows.append(["份量", serving_info, "", ""])

        # 添加营养成分行
        if "items" in result and isinstance(result["items"], list):
            for item in result["items"]:
                name = item.get("name", "")
                amount = item.get("amount", 0)
                unit = item.get("unit", "")
                nrv_percent = item.get("nrv_percent", 0)

                # 格式化NRV%
                nrv_display = (
                    f"{nrv_percent}%" if nrv_percent and nrv_percent > 0 else "-"
                )

                rows.append([name, str(amount), unit, nrv_display])

        return {
            "table_type": "nutrition_facts",
            "title": "营养成分表",
            "headers": headers,
            "rows": rows,
            "serving_info": serving_info,
        }

    def _create_summary_table(
        self, doc_success: bool, nutrition_success: bool, timestamp: float
    ) -> dict:
        """创建执行摘要表格"""
        headers = ["项目", "状态", "说明"]
        rows = [
            ["文档分析", "✓ 成功" if doc_success else "✗ 失败", "多查询文档信息提取"],
            [
                "营养成分表提取",
                "✓ 成功" if nutrition_success else "✗ 失败",
                "图片OCR识别",
            ],
            ["执行时间", f"{timestamp:.2f}秒", "总处理时间"],
            [
                "整体状态",
                "✓ 完成" if doc_success and nutrition_success else "⚠ 部分完成",
                "工作流执行状态",
            ],
        ]

        return {
            "table_type": "execution_summary",
            "title": "执行摘要",
            "headers": headers,
            "rows": rows,
        }

    def _format_table_for_display(self, table_data: dict) -> str:
        """将表格数据格式化为可显示的字符串"""
        if (
            not table_data
            or not table_data.get("headers")
            or not table_data.get("rows")
        ):
            return f"表格 '{table_data.get('title', '未知')}' 无数据"

        title = table_data.get("title", "")
        headers = table_data["headers"]
        rows = table_data["rows"]

        # 计算每列的最大宽度
        col_widths = [len(str(header)) for header in headers]
        for row in rows:
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    col_widths[i] = max(col_widths[i], len(str(cell)))

        # 构建表格字符串
        result = f"\n{title}\n" + "=" * len(title) + "\n"

        # 表头
        header_row = " | ".join(
            str(headers[i]).ljust(col_widths[i]) for i in range(len(headers))
        )
        result += header_row + "\n"
        result += "-" * len(header_row) + "\n"

        # 数据行
        for row in rows:
            data_row = " | ".join(
                str(row[i]).ljust(col_widths[i]) for i in range(len(row))
            )
            result += data_row + "\n"

        return result

    def _create_excel_file(
        self, doc_table: dict, nutrition_table: dict, summary_table: dict
    ) -> str:
        """创建Excel文件并返回文件路径"""
        try:
            # 创建输出目录
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)

            # 生成文件名（带时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_file = output_dir / f"limit_extract_results_{timestamp}.xlsx"

            # 创建Excel写入器
            with pd.ExcelWriter(excel_file, engine="openpyxl") as writer:
                # 1. 文档分析结果表
                if doc_table.get("rows"):
                    doc_df = pd.DataFrame(
                        doc_table["rows"], columns=doc_table["headers"]
                    )
                    doc_df.to_excel(writer, sheet_name="文档分析结果", index=False)

                    # 设置列宽
                    worksheet = writer.sheets["文档分析结果"]
                    worksheet.column_dimensions["A"].width = 30  # 查询项目
                    worksheet.column_dimensions["B"].width = 50  # 提取结果
                    worksheet.column_dimensions["C"].width = 15  # 结果类型

                # 2. 营养成分表
                if nutrition_table.get("rows"):
                    nutrition_df = pd.DataFrame(
                        nutrition_table["rows"], columns=nutrition_table["headers"]
                    )
                    nutrition_df.to_excel(writer, sheet_name="营养成分表", index=False)

                    # 设置列宽
                    worksheet = writer.sheets["营养成分表"]
                    worksheet.column_dimensions["A"].width = 15  # 营养成分
                    worksheet.column_dimensions["B"].width = 10  # 含量
                    worksheet.column_dimensions["C"].width = 10  # 单位
                    worksheet.column_dimensions["D"].width = 10  # NRV%

                # 3. 执行摘要
                if summary_table.get("rows"):
                    summary_df = pd.DataFrame(
                        summary_table["rows"], columns=summary_table["headers"]
                    )
                    summary_df.to_excel(writer, sheet_name="执行摘要", index=False)

                    # 设置列宽
                    worksheet = writer.sheets["执行摘要"]
                    worksheet.column_dimensions["A"].width = 20  # 项目
                    worksheet.column_dimensions["B"].width = 15  # 状态
                    worksheet.column_dimensions["C"].width = 25  # 说明

                # 4. 创建汇总表（将所有信息整合到一个表中）
                summary_data = []

                # 添加文档分析结果
                if doc_table.get("rows"):
                    for row in doc_table["rows"]:
                        summary_data.append(
                            {
                                "数据类型": "文档分析",
                                "项目": row[0],  # 查询项目
                                "结果": row[1],  # 提取结果
                                "分类": row[2],  # 结果类型
                                "数值": "",
                                "单位": "",
                            }
                        )

                # 添加营养成分数据
                if nutrition_table.get("rows"):
                    for row in nutrition_table["rows"]:
                        if row[0] not in ["份量", "状态", "错误信息"]:  # 跳过特殊行
                            # 安全地获取行数据
                            name = row[0] if len(row) > 0 else ""
                            amount = row[1] if len(row) > 1 else ""
                            unit = row[2] if len(row) > 2 else ""

                            # 构建结果字符串
                            if unit:
                                result_str = f"{amount} {unit}"
                            else:
                                result_str = str(amount)

                            summary_data.append(
                                {
                                    "数据类型": "营养成分",
                                    "项目": name,  # 营养成分名称
                                    "结果": result_str,  # 含量+单位
                                    "分类": "营养指标",
                                    "数值": str(amount),  # 数值
                                    "单位": unit,  # 单位
                                }
                            )

                if summary_data:
                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name="数据汇总", index=False)

                    # 设置列宽
                    worksheet = writer.sheets["数据汇总"]
                    worksheet.column_dimensions["A"].width = 12  # 数据类型
                    worksheet.column_dimensions["B"].width = 25  # 项目
                    worksheet.column_dimensions["C"].width = 40  # 结果
                    worksheet.column_dimensions["D"].width = 15  # 分类
                    worksheet.column_dimensions["E"].width = 10  # 数值
                    worksheet.column_dimensions["F"].width = 10  # 单位

            logger.info(f"Excel文件已生成: {excel_file}")
            return str(excel_file)

        except Exception as e:
            logger.error(f"生成Excel文件失败: {e}")
            return ""

    @step
    async def compose_table(
        self, ctx: Context, ev: ComposeResultEvent
    ) -> StopEvent | ErrorEvent | None:
        """组合最终结果表格"""
        try:
            logger.info("开始组合最终结果表格")

            # 验证输入数据
            if not ev.doc_data and not ev.nutrition_data:
                logger.error("文档数据和营养数据都为空")
                ctx.send_event(
                    ErrorEvent(
                        error_type="validation",
                        error_message="无法组合表格：文档数据和营养数据都为空",
                        error_details={
                            "step": "compose_table",
                            "doc_data_empty": not bool(ev.doc_data),
                            "nutrition_data_empty": not bool(ev.nutrition_data),
                        },
                        step_name="compose_table",
                    )
                )
                return

            # 检查成功状态
            doc_success = "error" not in ev.doc_data
            nutrition_success = "error" not in ev.nutrition_data
            timestamp = asyncio.get_event_loop().time()

            # 创建各种表格
            try:
                doc_table = self._create_document_analysis_table(ev.doc_data)
                nutrition_table = self._create_nutrition_table(ev.nutrition_data)
                summary_table = self._create_summary_table(
                    doc_success, nutrition_success, timestamp
                )
            except Exception as e:
                logger.error(f"创建表格失败: {e}")
                ctx.send_event(
                    ErrorEvent(
                        error_type="processing",
                        error_message=f"表格创建失败: {str(e)}",
                        error_details={
                            "step": "compose_table",
                            "substep": "create_tables",
                        },
                        step_name="compose_table",
                    )
                )
                return

            # 生成Excel文件
            try:
                excel_file_path = self._create_excel_file(
                    doc_table, nutrition_table, summary_table
                )
                if not excel_file_path:
                    raise ValueError("Excel文件生成失败")
            except Exception as e:
                logger.error(f"Excel文件生成失败: {e}")
                ctx.send_event(
                    ErrorEvent(
                        error_type="processing",
                        error_message=f"Excel文件生成失败: {str(e)}",
                        error_details={
                            "step": "compose_table",
                            "substep": "create_excel",
                        },
                        step_name="compose_table",
                    )
                )
                return

            # 生成表格显示文本
            table_displays = {
                "document_analysis": self._format_table_for_display(doc_table),
                "nutrition_facts": self._format_table_for_display(nutrition_table),
                "execution_summary": self._format_table_for_display(summary_table),
            }

            # 构建最终结果
            final_result = {
                "excel_file": excel_file_path,
                "tables": {
                    "document_analysis": doc_table,
                    "nutrition_facts": nutrition_table,
                    "execution_summary": summary_table,
                },
                "table_displays": table_displays,
                "raw_data": {
                    "document_analysis": ev.doc_data,
                    "nutrition_table": ev.nutrition_data,
                },
                "metadata": {
                    "workflow_version": "1.0",
                    "processing_time": timestamp,
                    "total_queries": len(ev.doc_data.get("results", {}))
                    if doc_success
                    else 0,
                    "success_rate": {
                        "document_analysis": doc_success,
                        "nutrition_extraction": nutrition_success,
                        "overall": doc_success and nutrition_success,
                    },
                    "excel_generated": bool(excel_file_path),
                },
            }

            logger.info("结果表格组合完成")
            return StopEvent(result=final_result)

        except Exception as e:
            logger.error(f"结果组合失败: {e}")
            # 发送错误事件
            ctx.send_event(
                ErrorEvent(
                    error_type="system",
                    error_message=f"结果组合系统错误: {str(e)}",
                    error_details={"step": "compose_table"},
                    step_name="compose_table",
                )
            )

    @step
    async def handle_error(self, ctx: Context, ev: ErrorEvent) -> StopEvent:
        """处理错误事件并生成错误报告"""
        try:
            logger.error(f"处理错误事件: {ev.error_type} - {ev.error_message}")

            # 创建错误报告
            error_report = {
                "status": "error",
                "error_type": ev.error_type,
                "error_message": ev.error_message,
                "step_name": ev.step_name,
                "error_details": ev.error_details,
                "timestamp": asyncio.get_event_loop().time(),
                "workflow_version": "1.0",
            }

            # 根据错误类型提供不同的建议
            suggestions = {
                "validation": [
                    "请检查输入参数是否完整",
                    "确保 file_path 和 image_path 是有效的路径",
                    "确保 user_id 是非空字符串",
                    "确保 queries 是非空的字符串列表",
                ],
                "processing": [
                    "请检查文件是否存在且可读",
                    "检查网络连接和API配置",
                    "确保依赖服务正常运行",
                ],
                "system": [
                    "请检查系统资源是否充足",
                    "检查日志文件获取更多详细信息",
                    "联系系统管理员",
                ],
            }

            error_report["suggestions"] = suggestions.get(
                ev.error_type, ["请查看错误详情并重试"]
            )

            logger.info("错误处理完成，返回错误报告")
            return StopEvent(result=error_report)

        except Exception as e:
            logger.error(f"错误处理函数本身发生错误: {e}")
            return StopEvent(
                result={
                    "status": "critical_error",
                    "error_message": f"错误处理失败: {str(e)}",
                    "original_error": ev.error_message,
                }
            )


async def main():
    """测试主函数"""
    # 使用测试数据
    file_path = Path("data/Q 04A4788S.pdf")
    image_path = Path("test/llm-ocr-test/data/test/1.png")
    workflow_data = {
        "file_path": file_path,
        "image_path": image_path,
        "user_id": "test_user",
        "queries": [
            "铅的限量标准是多少？",
            "微生物指标的要求是什么？",
            "产品的保质期是多长时间？",
            "贮存条件有什么要求？",
            "感官要求包括哪些项目？",
        ],
    }

    # 运行workflow
    w = LimitExtractWorkflow(timeout=60, verbose=True)
    result = await w.run(data=workflow_data)

    print("Workflow执行完成:")

    # 显示Excel文件信息
    if "excel_file" in result and result["excel_file"]:
        print(f"\n✅ Excel文件已生成: {result['excel_file']}")
        excel_path = Path(result["excel_file"])
        if excel_path.exists():
            file_size = excel_path.stat().st_size
            print(f"📁 文件大小: {file_size} 字节")
            print(f"📊 包含工作表: 文档分析结果、营养成分表、执行摘要、数据汇总")
        else:
            print("❌ Excel文件生成失败")

    # 显示表格预览
    if "table_displays" in result:
        print("\n" + "=" * 80)
        print("表格预览")
        print("=" * 80)

        for table_name, table_display in result["table_displays"].items():
            print(table_display)
            print("\n" + "-" * 60 + "\n")

    # 显示元数据
    if "metadata" in result:
        metadata = result["metadata"]
        print("\n" + "=" * 80)
        print("执行统计")
        print("=" * 80)
        print(f"工作流版本: {metadata.get('workflow_version', 'N/A')}")
        print(f"处理时间: {metadata.get('processing_time', 0):.2f} 秒")
        print(f"查询总数: {metadata.get('total_queries', 0)}")
        print(
            f"文档分析成功: {'✅' if metadata.get('success_rate', {}).get('document_analysis') else '❌'}"
        )
        print(
            f"营养成分提取成功: {'✅' if metadata.get('success_rate', {}).get('nutrition_extraction') else '❌'}"
        )
        print(f"Excel生成成功: {'✅' if metadata.get('excel_generated') else '❌'}")

    # 显示简化的JSON结果（不包含完整的raw_data）
    print("\n" + "=" * 80)
    print("结果摘要")
    print("=" * 80)
    summary_result = {
        "excel_file": result.get("excel_file", ""),
        "metadata": result.get("metadata", {}),
        "table_count": len(result.get("tables", {})),
    }
    print(json.dumps(summary_result, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
