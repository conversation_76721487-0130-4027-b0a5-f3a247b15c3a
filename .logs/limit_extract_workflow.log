2025-06-29 20:47:45,512 - limit_extract_workflow - ERROR - 缺少必需参数: file_path (PDF文件路径), image_path (营养成分表图片路径), user_id (用户ID), queries (查询列表)
2025-06-29 20:47:45,512 - limit_extract_workflow - ERROR - 参数验证或事件分发失败: 缺少必需参数: file_path (PDF文件路径), image_path (营养成分表图片路径), user_id (用户ID), queries (查询列表)
2025-06-29 20:47:45,513 - limit_extract_workflow - ERROR - 处理错误事件: validation - 缺少必需参数: file_path (PDF文件路径), image_path (营养成分表图片路径), user_id (用户ID), queries (查询列表)
2025-06-29 20:47:45,513 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 20:47:45,519 - limit_extract_workflow - ERROR - 缺少必需参数: image_path (营养成分表图片路径)
2025-06-29 20:47:45,519 - limit_extract_workflow - ERROR - 参数验证或事件分发失败: 缺少必需参数: image_path (营养成分表图片路径)
2025-06-29 20:47:45,519 - limit_extract_workflow - ERROR - 处理错误事件: validation - 缺少必需参数: image_path (营养成分表图片路径)
2025-06-29 20:47:45,519 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 20:47:45,525 - limit_extract_workflow - ERROR - 参数验证或事件分发失败: queries 必须是非空列表
2025-06-29 20:47:45,525 - limit_extract_workflow - ERROR - 处理错误事件: validation - queries 必须是非空列表
2025-06-29 20:47:45,525 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 20:47:45,530 - limit_extract_workflow - ERROR - 缺少必需参数: image_path (营养成分表图片路径), queries (查询列表)
2025-06-29 20:47:45,531 - limit_extract_workflow - ERROR - 参数验证或事件分发失败: 缺少必需参数: image_path (营养成分表图片路径), queries (查询列表)
2025-06-29 20:47:45,531 - limit_extract_workflow - ERROR - 处理错误事件: validation - 缺少必需参数: image_path (营养成分表图片路径), queries (查询列表)
2025-06-29 20:47:45,531 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 20:48:19,253 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: test/llm-ocr-test/data/test/0.png
2025-06-29 20:48:19,253 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 20:48:19,253 - limit_extract_workflow - INFO - 查询项目数量: 3
2025-06-29 20:48:19,253 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？', '微生物指标的要求是什么？', '产品的保质期是多长时间？']
2025-06-29 20:48:19,253 - limit_extract_workflow - INFO - 开始提取营养成分表: test/llm-ocr-test/data/test/0.png
2025-06-29 20:48:23,835 - limit_extract_workflow - WARNING - OCR结果解析失败: OCR返回的数据格式不正确，使用模拟数据
2025-06-29 20:48:23,836 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 20:48:23,843 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 20:48:23,844 - limit_extract_workflow - INFO - 开始执行 3 个查询
2025-06-29 20:48:23,844 - limit_extract_workflow - INFO - 执行查询 1/3: 铅的限量标准是多少？
2025-06-29 20:48:24,966 - limit_extract_workflow - ERROR - 查询失败 '铅的限量标准是多少？': Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-xhhwB***************************************lngH. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-06-29 20:48:24,966 - limit_extract_workflow - INFO - 执行查询 2/3: 微生物指标的要求是什么？
2025-06-29 20:48:25,435 - limit_extract_workflow - ERROR - 查询失败 '微生物指标的要求是什么？': Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-xhhwB***************************************lngH. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-06-29 20:48:25,435 - limit_extract_workflow - INFO - 执行查询 3/3: 产品的保质期是多长时间？
2025-06-29 20:48:25,787 - limit_extract_workflow - ERROR - 查询失败 '产品的保质期是多长时间？': Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-xhhwB***************************************lngH. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-06-29 20:48:25,787 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 20:48:25,787 - limit_extract_workflow - INFO - 结果收集完成
2025-06-29 20:48:25,788 - limit_extract_workflow - INFO - 开始组合最终结果表格
2025-06-29 20:48:25,894 - limit_extract_workflow - INFO - Excel文件已生成: output/limit_extract_results_20250629_204825.xlsx
2025-06-29 20:48:25,894 - limit_extract_workflow - INFO - 结果表格组合完成
2025-06-29 20:54:46,603 - limit_extract_workflow - ERROR - 缺少必需参数: image_path (营养成分表图片路径), user_id (用户ID), queries (查询列表)
2025-06-29 20:54:46,603 - limit_extract_workflow - ERROR - 参数验证或事件分发失败: 缺少必需参数: image_path (营养成分表图片路径), user_id (用户ID), queries (查询列表)
2025-06-29 20:54:46,603 - limit_extract_workflow - ERROR - 处理错误事件: validation - 缺少必需参数: image_path (营养成分表图片路径), user_id (用户ID), queries (查询列表)
2025-06-29 20:54:46,603 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 20:54:46,609 - limit_extract_workflow - ERROR - 参数验证或事件分发失败: queries 必须是非空列表
2025-06-29 20:54:46,609 - limit_extract_workflow - ERROR - 处理错误事件: validation - queries 必须是非空列表
2025-06-29 20:54:46,610 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 20:54:46,615 - limit_extract_workflow - INFO - 开始处理文件: nonexistent_file.pdf, 图片: test/llm-ocr-test/data/test/0.png
2025-06-29 20:54:46,615 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 20:54:46,615 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 20:54:46,615 - limit_extract_workflow - INFO - 查询项目: ['测试查询']
2025-06-29 20:54:46,616 - limit_extract_workflow - INFO - 开始提取营养成分表: test/llm-ocr-test/data/test/0.png
2025-06-29 20:54:55,224 - limit_extract_workflow - WARNING - OCR结果解析失败: OCR返回的数据格式不正确，使用模拟数据
2025-06-29 20:54:55,225 - limit_extract_workflow - INFO - 开始解析文档: nonexistent_file.pdf
2025-06-29 20:54:55,225 - limit_extract_workflow - ERROR - 文档解析失败: [Errno 2] No such file or directory: 'nonexistent_file.pdf'
2025-06-29 20:54:55,225 - limit_extract_workflow - ERROR - 处理错误事件: processing - 文档解析失败: [Errno 2] No such file or directory: 'nonexistent_file.pdf'
2025-06-29 20:54:55,225 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 20:54:55,230 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: .
2025-06-29 20:54:55,230 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 20:54:55,231 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 20:54:55,231 - limit_extract_workflow - INFO - 查询项目: ['测试查询']
2025-06-29 20:54:55,231 - limit_extract_workflow - INFO - 开始提取营养成分表: .
2025-06-29 20:54:55,231 - limit_extract_workflow - WARNING - OCR识别失败: [Errno 21] Is a directory: '.'，使用模拟数据
2025-06-29 20:54:55,231 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 20:54:55,232 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 20:54:55,232 - limit_extract_workflow - INFO - 开始执行 1 个查询
2025-06-29 20:54:55,232 - limit_extract_workflow - INFO - 执行查询 1/1: 测试查询
2025-06-29 20:54:55,964 - limit_extract_workflow - ERROR - 查询失败 '测试查询': Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-xhhwB***************************************lngH. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-06-29 20:54:55,964 - limit_extract_workflow - ERROR - 所有查询都失败了
2025-06-29 20:54:55,964 - limit_extract_workflow - ERROR - 处理错误事件: processing - 所有文档查询都失败了
2025-06-29 20:54:55,964 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 20:54:55,971 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: test/llm-ocr-test/data/test/0.png
2025-06-29 20:54:55,971 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 20:54:55,971 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 20:54:55,971 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？']
2025-06-29 20:54:55,971 - limit_extract_workflow - INFO - 开始提取营养成分表: test/llm-ocr-test/data/test/0.png
2025-06-29 20:55:05,000 - limit_extract_workflow - WARNING - OCR结果解析失败: OCR返回的数据格式不正确，使用模拟数据
2025-06-29 20:55:05,000 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 20:55:05,001 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 20:55:05,001 - limit_extract_workflow - INFO - 开始执行 1 个查询
2025-06-29 20:55:05,001 - limit_extract_workflow - INFO - 执行查询 1/1: 铅的限量标准是多少？
2025-06-29 20:55:05,691 - limit_extract_workflow - ERROR - 查询失败 '铅的限量标准是多少？': Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-xhhwB***************************************lngH. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-06-29 20:55:05,692 - limit_extract_workflow - ERROR - 所有查询都失败了
2025-06-29 20:55:05,692 - limit_extract_workflow - ERROR - 处理错误事件: processing - 所有文档查询都失败了
2025-06-29 20:55:05,692 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 21:07:10,521 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: test/llm-ocr-test/data/test/0.png
2025-06-29 21:07:10,521 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 21:07:10,521 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 21:07:10,521 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？']
2025-06-29 21:07:10,521 - limit_extract_workflow - INFO - 开始提取营养成分表: test/llm-ocr-test/data/test/0.png
2025-06-29 21:07:13,982 - limit_extract_workflow - WARNING - OCR结果解析失败: OCR返回的数据格式不正确，使用模拟数据
2025-06-29 21:07:13,982 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 21:07:13,984 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 21:07:13,984 - limit_extract_workflow - INFO - 开始执行 1 个查询
2025-06-29 21:07:13,984 - limit_extract_workflow - INFO - 执行查询 1/1: 铅的限量标准是多少？
2025-06-29 21:07:16,048 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 21:07:16,049 - limit_extract_workflow - INFO - 结果收集完成
2025-06-29 21:07:16,049 - limit_extract_workflow - INFO - 开始组合最终结果表格
2025-06-29 21:07:16,254 - limit_extract_workflow - INFO - Excel文件已生成: output/limit_extract_results_20250629_210716.xlsx
2025-06-29 21:07:16,254 - limit_extract_workflow - INFO - 结果表格组合完成
2025-06-29 21:09:42,936 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: test/llm-ocr-test/data/test/0.png
2025-06-29 21:09:42,936 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 21:09:42,936 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 21:09:42,936 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？']
2025-06-29 21:09:42,936 - limit_extract_workflow - INFO - 开始提取营养成分表: test/llm-ocr-test/data/test/0.png
2025-06-29 21:09:46,152 - limit_extract_workflow - WARNING - OCR结果解析失败: OCR返回的数据格式不正确，使用模拟数据
2025-06-29 21:09:46,153 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 21:09:46,154 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 21:09:46,155 - limit_extract_workflow - INFO - 开始执行 1 个查询
2025-06-29 21:09:46,155 - limit_extract_workflow - INFO - 执行查询 1/1: 铅的限量标准是多少？
2025-06-29 21:09:51,006 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 21:09:51,006 - limit_extract_workflow - INFO - 结果收集完成
2025-06-29 21:09:51,006 - limit_extract_workflow - INFO - 开始组合最终结果表格
2025-06-29 21:09:51,213 - limit_extract_workflow - INFO - Excel文件已生成: output/limit_extract_results_20250629_210951.xlsx
2025-06-29 21:09:51,213 - limit_extract_workflow - INFO - 结果表格组合完成
2025-06-29 21:15:02,380 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: nonexistent_image.png
2025-06-29 21:15:02,380 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 21:15:02,380 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 21:15:02,380 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？']
2025-06-29 21:15:02,380 - limit_extract_workflow - INFO - 开始提取营养成分表: nonexistent_image.png
2025-06-29 21:15:02,380 - limit_extract_workflow - ERROR - 图片文件不存在: nonexistent_image.png
2025-06-29 21:15:02,380 - limit_extract_workflow - ERROR - 处理错误事件: validation - 营养成分表图片文件不存在: nonexistent_image.png
2025-06-29 21:15:02,380 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 21:15:02,380 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 21:15:02,381 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 21:15:02,381 - limit_extract_workflow - INFO - 开始执行 1 个查询
2025-06-29 21:15:02,382 - limit_extract_workflow - INFO - 执行查询 1/1: 铅的限量标准是多少？
2025-06-29 21:15:04,487 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 21:15:04,494 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: .
2025-06-29 21:15:04,494 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 21:15:04,494 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 21:15:04,494 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？']
2025-06-29 21:15:04,494 - limit_extract_workflow - INFO - 开始提取营养成分表: .
2025-06-29 21:15:04,494 - limit_extract_workflow - ERROR - OCR识别过程失败: [Errno 21] Is a directory: '.'
2025-06-29 21:15:04,494 - limit_extract_workflow - ERROR - 处理错误事件: processing - 营养成分表OCR识别过程失败: [Errno 21] Is a directory: '.'
2025-06-29 21:15:04,494 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 21:15:04,494 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 21:15:04,495 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 21:15:04,496 - limit_extract_workflow - INFO - 开始执行 1 个查询
2025-06-29 21:15:04,496 - limit_extract_workflow - INFO - 执行查询 1/1: 铅的限量标准是多少？
2025-06-29 21:15:11,165 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 21:15:11,174 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: test/test_image.png
2025-06-29 21:15:11,174 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 21:15:11,174 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 21:15:11,174 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？']
2025-06-29 21:15:11,175 - limit_extract_workflow - INFO - 开始提取营养成分表: test/test_image.png
2025-06-29 21:16:02,281 - limit_extract_workflow - ERROR - LLM OCR失败: Error code: 400 - {'error': {'message': 'You uploaded an unsupported image. Please make sure your image is valid.【如果您遇到问题，欢迎加入QQ群咨询：1048463714】', 'type': 'invalid_request_error', 'param': None, 'code': 'image_parse_error'}}
2025-06-29 21:16:02,282 - limit_extract_workflow - ERROR - OCR识别失败: OCR识别失败: Error code: 400 - {'error': {'message': 'You uploaded an unsupported image. Please make sure your image is valid.【如果您遇到问题，欢迎加入QQ群咨询：1048463714】', 'type': 'invalid_request_error', 'param': None, 'code': 'image_parse_error'}}
2025-06-29 21:16:02,282 - limit_extract_workflow - ERROR - 处理错误事件: processing - 营养成分表OCR识别失败: OCR识别失败: Error code: 400 - {'error': {'message': 'You uploaded an unsupported image. Please make sure your image is valid.【如果您遇到问题，欢迎加入QQ群咨询：1048463714】', 'type': 'invalid_request_error', 'param': None, 'code': 'image_parse_error'}}
2025-06-29 21:16:02,282 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 21:16:02,282 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 21:16:02,283 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 21:16:02,283 - limit_extract_workflow - INFO - 开始执行 1 个查询
2025-06-29 21:16:02,283 - limit_extract_workflow - INFO - 执行查询 1/1: 铅的限量标准是多少？
2025-06-29 21:16:04,365 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 21:16:04,373 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: test/llm-ocr-test/data/test/0.png
2025-06-29 21:16:04,374 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 21:16:04,374 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 21:16:04,374 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？']
2025-06-29 21:16:04,374 - limit_extract_workflow - INFO - 开始提取营养成分表: test/llm-ocr-test/data/test/0.png
2025-06-29 21:16:08,073 - limit_extract_workflow - ERROR - OCR识别失败: 未检测到营养成分表
2025-06-29 21:16:08,074 - limit_extract_workflow - ERROR - 处理错误事件: processing - 营养成分表OCR识别失败: 未检测到营养成分表
2025-06-29 21:16:08,074 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 21:16:08,074 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 21:16:08,075 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 21:16:08,076 - limit_extract_workflow - INFO - 开始执行 1 个查询
2025-06-29 21:16:08,076 - limit_extract_workflow - INFO - 执行查询 1/1: 铅的限量标准是多少？
2025-06-29 21:16:10,105 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 21:33:27,422 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: nonexistent_image.png
2025-06-29 21:33:27,422 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 21:33:27,422 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 21:33:27,422 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？']
2025-06-29 21:33:27,423 - limit_extract_workflow - INFO - 开始提取营养成分表: nonexistent_image.png
2025-06-29 21:33:27,423 - limit_extract_workflow - ERROR - 图片文件不存在: nonexistent_image.png
2025-06-29 21:33:27,423 - limit_extract_workflow - ERROR - 处理错误事件: validation - 营养成分表图片文件不存在: nonexistent_image.png
2025-06-29 21:33:27,423 - limit_extract_workflow - INFO - 错误处理完成，返回错误报告
2025-06-29 21:33:27,423 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 21:33:27,424 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 21:33:27,424 - limit_extract_workflow - INFO - 开始执行 1 个查询
2025-06-29 21:33:27,424 - limit_extract_workflow - INFO - 执行查询 1/1: 铅的限量标准是多少？
2025-06-29 21:33:29,566 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 21:33:29,573 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: test/llm-ocr-test/data/test/0.png
2025-06-29 21:33:29,573 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 21:33:29,574 - limit_extract_workflow - INFO - 查询项目数量: 1
2025-06-29 21:33:29,574 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？']
2025-06-29 21:33:29,574 - limit_extract_workflow - INFO - 开始提取营养成分表: test/llm-ocr-test/data/test/0.png
2025-06-29 21:33:33,362 - limit_extract_workflow - INFO - 营养成分表OCR返回字符串结果: 未检测到营养成分表
2025-06-29 21:33:33,362 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 21:33:33,363 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 21:33:33,364 - limit_extract_workflow - INFO - 开始执行 1 个查询
2025-06-29 21:33:33,364 - limit_extract_workflow - INFO - 执行查询 1/1: 铅的限量标准是多少？
2025-06-29 21:33:35,256 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 21:33:35,256 - limit_extract_workflow - INFO - 结果收集完成
2025-06-29 21:33:35,256 - limit_extract_workflow - INFO - 开始组合最终结果表格
2025-06-29 21:33:35,317 - limit_extract_workflow - INFO - Excel文件已生成: output/limit_extract_results_20250629_213335.xlsx
2025-06-29 21:33:35,317 - limit_extract_workflow - INFO - 结果表格组合完成
2025-06-29 21:42:44,692 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: test/llm-ocr-test/data/test/0.png
2025-06-29 21:42:44,693 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 21:42:44,693 - limit_extract_workflow - INFO - 查询项目数量: 5
2025-06-29 21:42:44,693 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？', '微生物指标的要求是什么？', '产品的保质期是多长时间？', '贮存条件有什么要求？', '感官要求包括哪些项目？']
2025-06-29 21:42:44,693 - limit_extract_workflow - INFO - 开始提取营养成分表: test/llm-ocr-test/data/test/0.png
2025-06-29 21:42:49,031 - limit_extract_workflow - INFO - 营养成分表OCR返回字符串结果: 未检测到营养成分表
2025-06-29 21:42:49,031 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 21:42:49,032 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 21:42:49,032 - limit_extract_workflow - INFO - 开始执行 5 个查询
2025-06-29 21:42:49,032 - limit_extract_workflow - INFO - 执行查询 1/5: 铅的限量标准是多少？
2025-06-29 21:42:51,008 - limit_extract_workflow - INFO - 执行查询 2/5: 微生物指标的要求是什么？
2025-06-29 21:42:52,509 - limit_extract_workflow - INFO - 执行查询 3/5: 产品的保质期是多长时间？
2025-06-29 21:42:54,102 - limit_extract_workflow - INFO - 执行查询 4/5: 贮存条件有什么要求？
2025-06-29 21:42:56,566 - limit_extract_workflow - INFO - 执行查询 5/5: 感官要求包括哪些项目？
2025-06-29 21:42:58,403 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 21:42:58,404 - limit_extract_workflow - INFO - 结果收集完成
2025-06-29 21:42:58,404 - limit_extract_workflow - INFO - 开始组合最终结果表格
2025-06-29 21:42:58,589 - limit_extract_workflow - INFO - Excel文件已生成: output/limit_extract_results_20250629_214258.xlsx
2025-06-29 21:42:58,589 - limit_extract_workflow - INFO - 结果表格组合完成
2025-06-29 21:46:01,165 - limit_extract_workflow - INFO - 开始处理文件: data/Q 04A4788S.pdf, 图片: test/llm-ocr-test/data/test/1.png
2025-06-29 21:46:01,165 - limit_extract_workflow - INFO - 用户ID: test_user
2025-06-29 21:46:01,166 - limit_extract_workflow - INFO - 查询项目数量: 5
2025-06-29 21:46:01,166 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？', '微生物指标的要求是什么？', '产品的保质期是多长时间？', '贮存条件有什么要求？', '感官要求包括哪些项目？']
2025-06-29 21:46:01,166 - limit_extract_workflow - INFO - 开始提取营养成分表: test/llm-ocr-test/data/test/1.png
2025-06-29 21:46:05,928 - limit_extract_workflow - INFO - 营养成分表OCR识别成功
2025-06-29 21:46:05,928 - limit_extract_workflow - INFO - 开始解析文档: data/Q 04A4788S.pdf
2025-06-29 21:46:05,929 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 21:46:05,929 - limit_extract_workflow - INFO - 开始执行 5 个查询
2025-06-29 21:46:05,930 - limit_extract_workflow - INFO - 执行查询 1/5: 铅的限量标准是多少？
2025-06-29 21:46:07,869 - limit_extract_workflow - INFO - 执行查询 2/5: 微生物指标的要求是什么？
2025-06-29 21:46:09,366 - limit_extract_workflow - INFO - 执行查询 3/5: 产品的保质期是多长时间？
2025-06-29 21:46:10,859 - limit_extract_workflow - INFO - 执行查询 4/5: 贮存条件有什么要求？
2025-06-29 21:46:12,940 - limit_extract_workflow - INFO - 执行查询 5/5: 感官要求包括哪些项目？
2025-06-29 21:46:15,163 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 21:46:15,164 - limit_extract_workflow - INFO - 结果收集完成
2025-06-29 21:46:15,164 - limit_extract_workflow - INFO - 开始组合最终结果表格
2025-06-29 21:46:15,351 - limit_extract_workflow - INFO - Excel文件已生成: output/limit_extract_results_20250629_214615.xlsx
2025-06-29 21:46:15,351 - limit_extract_workflow - INFO - 结果表格组合完成
2025-06-29 22:48:41,991 - limit_extract_workflow - INFO - 开始处理文件: temp/test_user_web/b0ea8a6d-897c-4dd1-bfb4-5f421522bd3b.pdf, 图片: temp/test_user_web/e4679427-f9c0-45e0-86be-eb5a7d0f6f24.png
2025-06-29 22:48:41,991 - limit_extract_workflow - INFO - 用户ID: test_user_web
2025-06-29 22:48:41,991 - limit_extract_workflow - INFO - 查询项目数量: 3
2025-06-29 22:48:41,991 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？', '微生物指标的要求是什么？', '产品的保质期是多长时间？']
2025-06-29 22:48:41,991 - limit_extract_workflow - INFO - 开始提取营养成分表: temp/test_user_web/e4679427-f9c0-45e0-86be-eb5a7d0f6f24.png
2025-06-29 22:48:48,600 - limit_extract_workflow - INFO - 营养成分表OCR识别成功
2025-06-29 22:48:48,601 - limit_extract_workflow - INFO - 开始解析文档: temp/test_user_web/b0ea8a6d-897c-4dd1-bfb4-5f421522bd3b.pdf
2025-06-29 22:48:48,601 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 22:48:48,602 - limit_extract_workflow - INFO - 开始执行 3 个查询
2025-06-29 22:48:48,602 - limit_extract_workflow - INFO - 执行查询 1/3: 铅的限量标准是多少？
2025-06-29 22:48:50,512 - limit_extract_workflow - INFO - 执行查询 2/3: 微生物指标的要求是什么？
2025-06-29 22:48:51,968 - limit_extract_workflow - INFO - 执行查询 3/3: 产品的保质期是多长时间？
2025-06-29 22:48:53,467 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 22:48:53,467 - limit_extract_workflow - INFO - 结果收集完成
2025-06-29 22:48:53,468 - limit_extract_workflow - INFO - 开始组合最终结果表格
2025-06-29 22:48:53,524 - limit_extract_workflow - INFO - Excel文件已生成: output/limit_extract_results_20250629_224853.xlsx
2025-06-29 22:48:53,524 - limit_extract_workflow - INFO - 结果表格组合完成
2025-06-29 23:05:06,487 - limit_extract_workflow - INFO - 开始处理文件: temp/test_user_web/01a71e28-deae-4aef-aeee-94735bff5f5b.pdf, 图片: temp/test_user_web/f10a31a7-879e-4820-bd86-94171144a7aa.png
2025-06-29 23:05:06,487 - limit_extract_workflow - INFO - 用户ID: test_user_web
2025-06-29 23:05:06,488 - limit_extract_workflow - INFO - 查询项目数量: 3
2025-06-29 23:05:06,488 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？', '微生物指标的要求是什么？', '产品的保质期是多长时间？']
2025-06-29 23:05:06,488 - limit_extract_workflow - INFO - 开始提取营养成分表: temp/test_user_web/f10a31a7-879e-4820-bd86-94171144a7aa.png
2025-06-29 23:05:12,742 - limit_extract_workflow - INFO - 营养成分表OCR识别成功
2025-06-29 23:05:12,743 - limit_extract_workflow - INFO - 开始解析文档: temp/test_user_web/01a71e28-deae-4aef-aeee-94735bff5f5b.pdf
2025-06-29 23:05:12,745 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-29 23:05:12,746 - limit_extract_workflow - INFO - 开始执行 3 个查询
2025-06-29 23:05:12,746 - limit_extract_workflow - INFO - 执行查询 1/3: 铅的限量标准是多少？
2025-06-29 23:05:14,702 - limit_extract_workflow - INFO - 执行查询 2/3: 微生物指标的要求是什么？
2025-06-29 23:05:16,333 - limit_extract_workflow - INFO - 执行查询 3/3: 产品的保质期是多长时间？
2025-06-29 23:05:17,867 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-29 23:05:17,868 - limit_extract_workflow - INFO - 结果收集完成
2025-06-29 23:05:17,868 - limit_extract_workflow - INFO - 开始组合最终结果表格
2025-06-29 23:05:17,962 - limit_extract_workflow - INFO - Excel文件已生成: output/limit_extract_results_20250629_230517.xlsx
2025-06-29 23:05:17,962 - limit_extract_workflow - INFO - 结果表格组合完成
2025-06-30 08:48:21,563 - limit_extract_workflow - INFO - 开始处理文件: temp/test_user_web/9a6fda09-22b4-4d7f-baa4-cf37aebd6c62.pdf, 图片: temp/test_user_web/851439ba-f623-4e2c-9e5d-800ea0e6d6f0.png
2025-06-30 08:48:21,564 - limit_extract_workflow - INFO - 用户ID: test_user_web
2025-06-30 08:48:21,564 - limit_extract_workflow - INFO - 查询项目数量: 3
2025-06-30 08:48:21,564 - limit_extract_workflow - INFO - 查询项目: ['铅的限量标准是多少？', '微生物指标的要求是什么？', '产品的保质期是多长时间？']
2025-06-30 08:48:21,564 - limit_extract_workflow - INFO - 开始提取营养成分表: temp/test_user_web/851439ba-f623-4e2c-9e5d-800ea0e6d6f0.png
2025-06-30 08:48:28,706 - limit_extract_workflow - INFO - 营养成分表OCR识别成功
2025-06-30 08:48:28,707 - limit_extract_workflow - INFO - 开始解析文档: temp/test_user_web/9a6fda09-22b4-4d7f-baa4-cf37aebd6c62.pdf
2025-06-30 08:48:28,713 - limit_extract_workflow - INFO - 文档解析完成，开始搜索
2025-06-30 08:48:28,714 - limit_extract_workflow - INFO - 开始执行 3 个查询
2025-06-30 08:48:28,714 - limit_extract_workflow - INFO - 执行查询 1/3: 铅的限量标准是多少？
2025-06-30 08:48:31,961 - limit_extract_workflow - INFO - 执行查询 2/3: 微生物指标的要求是什么？
2025-06-30 08:48:33,503 - limit_extract_workflow - INFO - 执行查询 3/3: 产品的保质期是多长时间？
2025-06-30 08:48:35,755 - limit_extract_workflow - INFO - 文档搜索完成
2025-06-30 08:48:35,756 - limit_extract_workflow - INFO - 结果收集完成
2025-06-30 08:48:35,756 - limit_extract_workflow - INFO - 开始组合最终结果表格
2025-06-30 08:48:35,853 - limit_extract_workflow - INFO - Excel文件已生成: output/limit_extract_results_20250630_084835.xlsx
2025-06-30 08:48:35,853 - limit_extract_workflow - INFO - 结果表格组合完成
