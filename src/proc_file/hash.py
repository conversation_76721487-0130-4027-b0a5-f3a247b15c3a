from pathlib import Path
from abc import ABC, abstractmethod
import hashlib

from ..config import config


class HashCalculator(ABC):
    @abstractmethod
    def calculate_hash(self, file_path: Path) -> str:
        pass


class HashStore(ABC):
    @abstractmethod
    def has_hash(self, file_hash: str) -> bool:
        pass

    @abstractmethod
    def add_hash(self, file_hash: str) -> None:
        pass


class MD5HashCalculator(HashCalculator):
    def calculate_hash(self, file_path: Path) -> str:
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()


class SHA256HashCalculator(HashCalculator):
    def calculate_hash(self, file_path: Path) -> str:
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()


class InMemoryHashStore(HashStore):
    def __init__(self):
        self.hashes = set()

    def has_hash(self, file_hash: str) -> bool:
        return file_hash in self.hashes

    def add_hash(self, file_hash: str) -> None:
        self.hashes.add(file_hash)


class FileBasedHashStore(HashStore):
    def __init__(
        self,
        store_path: Path = Path(config.storage_dir) / "hashes.txt",
    ):
        self.store_path = store_path
        if not store_path.exists():
            store_path.parent.mkdir(parents=True, exist_ok=True)
            store_path.touch()

    def has_hash(self, file_hash: str) -> bool:
        with open(self.store_path, "r") as f:
            return file_hash in f.read()

    def add_hash(self, file_hash: str) -> None:
        with open(self.store_path, "a") as f:
            f.write(file_hash + "\n")
