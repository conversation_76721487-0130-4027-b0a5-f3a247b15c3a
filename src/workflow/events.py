"""
工作流事件定义
"""
from llama_index.core.workflow import Event
from pathlib import Path
from typing import List, Dict, Any

from ..data.models import DocumentAnalysisResult, NutritionExtractionResult


class WorkflowInputEvent(Event):
    """工作流输入事件"""
    file_path: Path
    image_path: Path
    queries: List[str]
    user_id: str


class DocParseEvent(Event):
    """文档解析事件"""
    file_path: Path
    user_id: str
    queries: List[str]


class DocSearchEvent(Event):
    """文档搜索事件"""
    queries: List[str]
    user_id: str


class NutritionParseEvent(Event):
    """营养成分表解析事件"""
    image_path: Path


class MergeEvent(Event):
    """合并事件"""
    source: str  # "doc_search" 或 "nutrition_table"
    data: Dict[str, Any]


class ComposeResultEvent(Event):
    """结果组合事件"""
    doc_result: DocumentAnalysisResult
    nutrition_result: NutritionExtractionResult
