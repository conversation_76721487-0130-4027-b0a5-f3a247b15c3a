from pathlib import Path
from typing import Annotated, Callable

import magic
from pydantic import BaseModel, Field

from ..converter.mineru_converter import mineru_converter
from ..log.loggers import trace_func, get_logger
from .duplicate_file_checker import duplicate_file_checker
from .indexer import indexer
from .uploader import uploader

logger = get_logger("proc_file")


class FileProcessContext(BaseModel):
    file_path: Path
    user_id: str
    converted_file_path: Annotated[
        Path | None, Field(default=None, description="转换后的文件路径")
    ]
    hash_value: Annotated[
        str | None, Field(default=None, description="文件的hash value")
    ]


class FileProcessor:
    def __init__(self):
        self.supported_file_types = ["application/pdf"]
        self.duplicate_file_checker = duplicate_file_checker
        self.converter = mineru_converter
        self.uploader = uploader
        self.indexer = indexer

    def _file_type_checker(self, context: FileProcessContext):
        file_path = context.file_path
        mime_type = magic.from_file(file_path, mime=True)
        if mime_type not in self.supported_file_types:
            raise ValueError("File type is not supported")

    def _check_dulicate_file(self, context: FileProcessContext) -> bool:
        result, hash_value = self.duplicate_file_checker.check(Path(context.file_path))
        context.hash_value = hash_value
        if result:
            return True
        else:
            return False

    @trace_func(logger=logger)
    def _on_duplicate(self, context: FileProcessContext) -> bool:
        # 更新用户文件列表
        res = True
        if not res:
            raise ValueError("Duplicate file handling failed")
        else:
            return True

    @trace_func(logger=logger)
    def _convert(self, context: FileProcessContext):
        converted_file_path = self.converter.convert(context.file_path)
        if converted_file_path is None:
            raise ValueError("Conversion failed")
        context.converted_file_path = converted_file_path

    @trace_func(logger=logger)
    def _upload(self, context: FileProcessContext):
        assert context.converted_file_path is not None
        is_uploaded = self.uploader.upload(context.converted_file_path)
        if not is_uploaded:
            raise ValueError("Upload failed")

    @trace_func(logger=logger)
    def _index(self, context: FileProcessContext):
        assert context.converted_file_path is not None
        self.indexer.add_doc(context.converted_file_path)

    def proc_file(self, file_path: Path, user_id: str):
        context = FileProcessContext(file_path=file_path, user_id=user_id)
        self._file_type_checker(context)

        is_duplicate = self._check_dulicate_file(context)

        if is_duplicate:
            logger.info("File is duplicate, skipping")
            self._on_duplicate(context)
            return True

        self._convert(context)
        self._upload(context)
        self._index(context)

        return True


if __name__ == "__main__":
    processer = FileProcessor()
    file_path = Path("/home/<USER>/debug/limit-extract/data/Q 04A4788S.pdf")
    processer.proc_file(file_path=file_path, user_id="test_user")
