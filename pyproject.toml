[project]
name = "limit-extract"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "chromadb>=1.0.12",
    "fastapi>=0.115.9",
    "huggingface-hub>=0.32.3",
    "ipykernel>=6.29.5",
    "ipython>=9.2.0",
    "ipywidgets>=8.1.7",
    "langchain-tavily>=0.1.6",
    "langchain[openai]>=0.3.25",
    "langgraph>=0.4.7",
    "langsmith>=0.3.42",
    "llama-index-embeddings-huggingface>=0.5.4",
    "llama-index-embeddings-openai>=0.3.1",
    "llama-index-llms-openai>=0.4.2",
    "magic-pdf[full]",
    "openpyxl>=3.1.5",
    "pandas>=2.2.3",
    "python-magic>=0.4.27",
    "python-multipart>=0.0.20",
    "pyyaml>=6.0.2",
    "rich>=14.0.0",
    "uvicorn>=0.34.3",
]

[tool.uv.sources]
magic-pdf = { path = "MinerU" }
