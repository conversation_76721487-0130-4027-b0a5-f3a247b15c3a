from fastapi import Fast<PERSON><PERSON>, Query, File, Form, UploadFile
from typing import List
import aiofiles
from pathlib import Path
from typing import Annotated
from pydantic import BaseModel
from uuid import uuid4, UUID

from ..engine import engine
from ..config import config


app = FastAPI()


@app.get("/api/limit_extract/task")
def get_task(user_id: Annotated[int, Query(description="user_id")]):
    res = engine.get_user_task()
    return res


SAVE_DIR: Path = Path(config.storage_dir) / "user_upload"
SAVE_DIR.mkdir(parents=True, exist_ok=True)


class Task(BaseModel):
    task_id: str
    user_id: str
    file_name: str
    image_name: str


@app.post("/api/file_process/task")
async def submit_task(
    user_id: Annotated[str, Form()],
    items_to_extract: Annotated[List[str], Form()],
    file: Annotated[
        UploadFile, File(description="用户上传的待解析的文件,通常是一个pdf")
    ],
    image: Annotated[UploadFile, File(description="用户上传的待解析的图片")],
):
    task_id = str(uuid4())
    # save_files
    task = Task(
        task_id=task_id,
        user_id=user_id,
        file_name=file.filename,
        image_name=image.filename,
    )
    for state in engine.submit_task(task):
        yield state


@app.get("/api/limit_extract/result")
async def get_result(task_id: Annotated[str, Query(description="task_id")]):
    res = engine.get_task_result(task_id)
    return res
