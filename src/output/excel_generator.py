"""
Excel文件生成器
"""
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

from ..data.models import TableData, DocumentAnalysisResult, NutritionExtractionResult
from ..utils.constants import ExcelConstants
from ..config.config import app_config
from ..log.loggers import get_logger

logger = get_logger("excel_generator")


class ExcelGenerator:
    """Excel文件生成器"""
    
    def __init__(self):
        self.config = app_config
        self.output_dir = self.config.paths.output_dir
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_excel(
        self, 
        doc_table: TableData, 
        nutrition_table: TableData, 
        summary_table: TableData
    ) -> str:
        """生成Excel文件并返回文件路径"""
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.config.excel.filename_template.format(timestamp=timestamp)
            excel_file = self.output_dir / filename
            
            # 创建Excel写入器
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                self._write_document_analysis_sheet(writer, doc_table)
                self._write_nutrition_sheet(writer, nutrition_table)
                self._write_summary_sheet(writer, summary_table)
                self._write_data_summary_sheet(writer, doc_table, nutrition_table)
            
            logger.info(f"Excel文件已生成: {excel_file}")
            return str(excel_file)
            
        except Exception as e:
            logger.error(f"生成Excel文件失败: {e}")
            return ""
    
    def _write_document_analysis_sheet(self, writer: pd.ExcelWriter, table_data: TableData):
        """写入文档分析结果工作表"""
        if not table_data.is_valid():
            return
        
        df = pd.DataFrame(table_data.rows, columns=table_data.headers)
        df.to_excel(writer, sheet_name=ExcelConstants.SheetNames.DOCUMENT_ANALYSIS, index=False)
        
        # 设置列宽
        worksheet = writer.sheets[ExcelConstants.SheetNames.DOCUMENT_ANALYSIS]
        worksheet.column_dimensions['A'].width = ExcelConstants.ColumnWidths.QUERY
        worksheet.column_dimensions['B'].width = ExcelConstants.ColumnWidths.RESULT
        worksheet.column_dimensions['C'].width = ExcelConstants.ColumnWidths.TYPE
    
    def _write_nutrition_sheet(self, writer: pd.ExcelWriter, table_data: TableData):
        """写入营养成分表工作表"""
        if not table_data.is_valid():
            return
        
        df = pd.DataFrame(table_data.rows, columns=table_data.headers)
        df.to_excel(writer, sheet_name=ExcelConstants.SheetNames.NUTRITION_FACTS, index=False)
        
        # 设置列宽
        worksheet = writer.sheets[ExcelConstants.SheetNames.NUTRITION_FACTS]
        worksheet.column_dimensions['A'].width = ExcelConstants.ColumnWidths.NUTRITION_NAME
        worksheet.column_dimensions['B'].width = ExcelConstants.ColumnWidths.NUTRITION_AMOUNT
        worksheet.column_dimensions['C'].width = ExcelConstants.ColumnWidths.NUTRITION_UNIT
        worksheet.column_dimensions['D'].width = ExcelConstants.ColumnWidths.NUTRITION_NRV
    
    def _write_summary_sheet(self, writer: pd.ExcelWriter, table_data: TableData):
        """写入执行摘要工作表"""
        if not table_data.is_valid():
            return
        
        df = pd.DataFrame(table_data.rows, columns=table_data.headers)
        df.to_excel(writer, sheet_name=ExcelConstants.SheetNames.EXECUTION_SUMMARY, index=False)
        
        # 设置列宽
        worksheet = writer.sheets[ExcelConstants.SheetNames.EXECUTION_SUMMARY]
        worksheet.column_dimensions['A'].width = ExcelConstants.ColumnWidths.SUMMARY_ITEM
        worksheet.column_dimensions['B'].width = ExcelConstants.ColumnWidths.SUMMARY_STATUS
        worksheet.column_dimensions['C'].width = ExcelConstants.ColumnWidths.SUMMARY_DESC
    
    def _write_data_summary_sheet(
        self, 
        writer: pd.ExcelWriter, 
        doc_table: TableData, 
        nutrition_table: TableData
    ):
        """写入数据汇总工作表"""
        summary_data = []
        
        # 添加文档分析结果
        if doc_table.is_valid():
            for row in doc_table.rows:
                summary_data.append({
                    "数据类型": "文档分析",
                    "项目": row[0],
                    "结果": row[1],
                    "分类": row[2],
                    "数值": "",
                    "单位": ""
                })
        
        # 添加营养成分数据
        if nutrition_table.is_valid():
            for row in nutrition_table.rows:
                if row[0] not in self.config.nutrition.skip_rows:
                    name = row[0] if len(row) > 0 else ""
                    amount = row[1] if len(row) > 1 else ""
                    unit = row[2] if len(row) > 2 else ""
                    
                    result_str = f"{amount} {unit}" if unit else str(amount)
                    
                    summary_data.append({
                        "数据类型": "营养成分",
                        "项目": name,
                        "结果": result_str,
                        "分类": "营养指标",
                        "数值": str(amount),
                        "单位": unit
                    })
        
        if summary_data:
            df = pd.DataFrame(summary_data)
            df.to_excel(writer, sheet_name=ExcelConstants.SheetNames.DATA_SUMMARY, index=False)
            
            # 设置列宽
            worksheet = writer.sheets[ExcelConstants.SheetNames.DATA_SUMMARY]
            worksheet.column_dimensions['A'].width = ExcelConstants.ColumnWidths.DATA_TYPE
            worksheet.column_dimensions['B'].width = ExcelConstants.ColumnWidths.DATA_ITEM
            worksheet.column_dimensions['C'].width = ExcelConstants.ColumnWidths.DATA_RESULT
            worksheet.column_dimensions['D'].width = ExcelConstants.ColumnWidths.DATA_CATEGORY
            worksheet.column_dimensions['E'].width = ExcelConstants.ColumnWidths.DATA_VALUE
            worksheet.column_dimensions['F'].width = ExcelConstants.ColumnWidths.DATA_UNIT
