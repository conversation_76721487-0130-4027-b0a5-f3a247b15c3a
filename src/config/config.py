import os
import yaml
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass
from pydantic import BaseModel, Field
from typing import Annotated


class Settings(BaseModel):
    storage_dir: Annotated[str, Field(description="Storage path")] = ".storage"


@dataclass
class APIConfig:
    """API配置"""

    openai_key: str
    openai_base_url: str
    model: str = "gpt-4o-mini"
    timeout: int = 30
    max_retries: int = 3


@dataclass
class WorkflowConfig:
    """工作流配置"""

    timeout: int = 60
    verbose: bool = True
    default_user_id: str = "test_user"


@dataclass
class PathConfig:
    """路径配置"""

    output_dir: Path
    test_data_dir: Path
    storage_dir: Path
    default_pdf: Path
    default_image: Path


@dataclass
class ExcelConfig:
    """Excel配置"""

    filename_template: str
    column_widths: Dict[str, int]


@dataclass
class NutritionConfig:
    """营养成分配置"""

    default_serving_size: int
    default_serving_unit: str
    skip_rows: List[str]


class Config:
    """统一配置管理类"""

    def __init__(self, config_file: str = None):
        self.project_root = Path(__file__).parent.parent.parent
        self.config_file = config_file or (Path(__file__).parent / "settings.yaml")
        self._load_config()
        self._setup_api_config()
        self._ensure_directories()

    def _load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            with open(self.config_file, "r", encoding="utf-8") as f:
                self._config_data = yaml.safe_load(f)
        else:
            self._config_data = self._get_default_config()

    def _setup_api_config(self):
        """设置API配置"""
        # 从环境变量获取API密钥
        openai_key = os.getenv("OPENAI_API_KEY", "")
        openai_base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

        api_config = self._config_data.get("api", {}).get("openai", {})

        self.api = APIConfig(
            openai_key=openai_key,
            openai_base_url=openai_base_url,
            model=api_config.get("model", "gpt-4o-mini"),
            timeout=api_config.get("timeout", 30),
            max_retries=api_config.get("max_retries", 3),
        )

    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.paths.output_dir,
            self.paths.storage_dir,
            self.paths.test_data_dir,
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    @property
    def workflow(self) -> WorkflowConfig:
        """工作流配置"""
        workflow_config = self._config_data.get("workflow", {})
        return WorkflowConfig(
            timeout=workflow_config.get("timeout", 60),
            verbose=workflow_config.get("verbose", True),
            default_user_id=workflow_config.get("default_user_id", "test_user"),
        )

    @property
    def paths(self) -> PathConfig:
        """路径配置"""
        paths_config = self._config_data.get("paths", {})
        return PathConfig(
            output_dir=self.project_root / paths_config.get("output_dir", "output"),
            test_data_dir=self.project_root
            / paths_config.get("test_data_dir", "test/data"),
            storage_dir=self.project_root
            / paths_config.get("storage_dir", ".storage/upload"),
            default_pdf=self.project_root
            / paths_config.get("default_pdf", "data/Q 04A4788S.pdf"),
            default_image=self.project_root
            / paths_config.get("default_image", "test/llm-ocr-test/data/test/0.png"),
        )

    @property
    def excel(self) -> ExcelConfig:
        """Excel配置"""
        excel_config = self._config_data.get("excel", {})
        return ExcelConfig(
            filename_template=excel_config.get(
                "filename_template", "limit_extract_results_{timestamp}.xlsx"
            ),
            column_widths=excel_config.get("column_widths", {}),
        )

    @property
    def nutrition(self) -> NutritionConfig:
        """营养成分配置"""
        nutrition_config = self._config_data.get("nutrition", {})
        return NutritionConfig(
            default_serving_size=nutrition_config.get("default_serving_size", 100),
            default_serving_unit=nutrition_config.get("default_serving_unit", "g"),
            skip_rows=nutrition_config.get("skip_rows", ["份量", "状态", "错误信息"]),
        )

    @property
    def default_queries(self) -> List[str]:
        """默认查询列表"""
        return self._config_data.get("queries", {}).get("default", [])

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "workflow": {"timeout": 60, "verbose": True},
            "paths": {"output_dir": "output"},
            "queries": {"default": []},
            "excel": {"filename_template": "limit_extract_results_{timestamp}.xlsx"},
            "nutrition": {"default_serving_size": 100, "default_serving_unit": "g"},
        }


# 全局配置实例
app_config = Config()

# 兼容性：保持原有的配置变量
config = {"storage": ".storage"}
config = Settings(**config)

# 原有变量保持兼容性
PROJECT_ROOT = app_config.project_root
STORAGE_DIR = app_config.paths.storage_dir.parent
UPLOAD_DIR = app_config.paths.storage_dir
INDEX_DIR = STORAGE_DIR / "index"

# 确保索引目录存在
INDEX_DIR.mkdir(parents=True, exist_ok=True)

# 原有的CONFIG字典（保持兼容性）
CONFIG = {
    "storage_dir": str(STORAGE_DIR),
    "upload_dir": str(UPLOAD_DIR),
    "index_dir": str(INDEX_DIR),
    "chunk_size": 1024,
    "chunk_overlap": 200,
    "embed_model": "BAAI/bge-small-en-v1.5",
    "llm_model": "gpt-3.5-turbo",
    "temperature": 0.1,
    "max_tokens": 1000,
}
