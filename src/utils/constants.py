"""
常量定义
"""
from enum import Enum
from typing import List


class QueryType(Enum):
    """查询类型枚举"""
    LIMIT_STANDARD = "限值标准"
    TECHNICAL_REQUIREMENT = "技术要求"
    SHELF_LIFE = "保质期信息"
    STORAGE_CONDITION = "贮存条件"
    SENSORY_INDICATOR = "感官指标"
    OTHER = "其他信息"


class NutritionConstants:
    """营养成分相关常量"""
    SKIP_ROWS: List[str] = ["份量", "状态", "错误信息"]
    DEFAULT_SERVING_SIZE: int = 100
    DEFAULT_SERVING_UNIT: str = "g"
    
    # 营养成分名称映射
    NUTRITION_NAMES = {
        "energy": "能量",
        "protein": "蛋白质", 
        "fat": "脂肪",
        "carbohydrate": "碳水化合物",
        "sugar": "糖",
        "sodium": "钠",
        "fiber": "膳食纤维"
    }


class ExcelConstants:
    """Excel相关常量"""
    class ColumnWidths:
        QUERY = 30
        RESULT = 50
        TYPE = 15
        NUTRITION_NAME = 15
        NUTRITION_AMOUNT = 10
        NUTRITION_UNIT = 10
        NUTRITION_NRV = 10
        SUMMARY_ITEM = 20
        SUMMARY_STATUS = 15
        SUMMARY_DESC = 25
        DATA_TYPE = 12
        DATA_ITEM = 25
        DATA_RESULT = 40
        DATA_CATEGORY = 15
        DATA_VALUE = 10
        DATA_UNIT = 10
    
    class SheetNames:
        DOCUMENT_ANALYSIS = "文档分析结果"
        NUTRITION_FACTS = "营养成分表"
        EXECUTION_SUMMARY = "执行摘要"
        DATA_SUMMARY = "数据汇总"


class WorkflowConstants:
    """工作流相关常量"""
    DEFAULT_TIMEOUT = 60
    DEFAULT_USER_ID = "test_user"
    
    # 默认查询列表
    DEFAULT_QUERIES = [
        "铅的限量标准是多少？",
        "微生物指标的要求是什么？",
        "产品的保质期是多长时间？",
        "贮存条件有什么要求？",
        "感官要求包括哪些项目？",
        "食品添加剂的使用标准是什么？",
        "净含量的检验方法是什么？",
        "原辅料的质量要求有哪些？"
    ]


class FileConstants:
    """文件相关常量"""
    OUTPUT_DIR = "output"
    TEST_DATA_DIR = "test/data"
    STORAGE_DIR = ".storage/upload"
    
    class Extensions:
        PDF = ".pdf"
        XLSX = ".xlsx"
        PNG = ".png"
        JPG = ".jpg"
        JPEG = ".jpeg"


class StatusConstants:
    """状态相关常量"""
    SUCCESS_SYMBOL = "✅"
    FAILURE_SYMBOL = "❌"
    WARNING_SYMBOL = "⚠"
    
    class Messages:
        SUCCESS = "成功"
        FAILURE = "失败"
        COMPLETED = "完成"
        PARTIAL_COMPLETED = "部分完成"
        OCR_FAILED = "OCR识别失败"
        QUERY_FAILED = "查询失败"
        FILE_NOT_FOUND = "文件不存在"
