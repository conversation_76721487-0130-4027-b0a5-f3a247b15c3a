from abc import ABC, abstractmethod
from pydantic import BaseModel, Field
from pathlib import Path
from typing import Annotated

from llama_index.core import (
    StorageContext,
    load_index_from_storage,
    VectorStoreIndex,
    SimpleDirectoryReader,
)

from ..config import config


class Indexer(ABC):
    @abstractmethod
    def add_doc(self, doc_path: Path) -> bool:
        pass

    @abstractmethod
    def query(self, string: str):
        pass


class QueryContent(BaseModel):
    query: str
    user_id: Annotated[str, Field(default="test_user")]


class VectorIndexer(Indexer):
    def __init__(self, storage_dir: Path) -> None:
        self.storage_dir: Path = storage_dir
        self.index_dir: Path = self.storage_dir / "index"
        self.index: None | VectorStoreIndex = self._try_load_index(self.storage_dir)

    def _try_load_index(self, storage_dir: Path) -> None | VectorStoreIndex:
        if self.index_dir.exists():
            # load
            self.storage_context = StorageContext.from_defaults(
                persist_dir=str(self.index_dir)
            )
            index = load_index_from_storage(self.storage_context)
            assert type(index) is VectorStoreIndex

            return index

    def _persist(self):
        assert type(self.index) is VectorStoreIndex, (
            "try to persist index but index is not VectorStoreIndex"
        )
        self.index.storage_context.persist(persist_dir=self.index_dir)

    def add_doc(self, doc_path: Path):
        """add doc to index

        Args:
            doc_path (Path): path to an existing document, now only support markdown

        Returns:
            bool: success or not
        """
        if self.index is None:
            self.index = VectorStoreIndex.from_documents(
                documents=SimpleDirectoryReader(input_files=[str(doc_path)]).load_data()
            )
            self._persist()
        else:
            docs = SimpleDirectoryReader(input_files=[str(doc_path)]).load_data()
            for doc in docs:
                self.index.insert(doc)

    def query(self, query: QueryContent) -> str:
        """query index, and return a result

        Args:
            query (QueryContent): query content

        Returns:
            str: answer
        """
        assert self.index is not None, "index is not initialized"
        query_engine = self.index.as_query_engine()
        result = query_engine.query(query.query)

        return str(result)


indexer = VectorIndexer(Path(config.storage_dir) / "index")

if __name__ == "__main__":
    indexer = VectorIndexer(Path("test_index"))
    indexer.add_doc(Path("./test/indexer-test/test.md"))
    print(indexer.query("what is the weather like in beijing?"))
