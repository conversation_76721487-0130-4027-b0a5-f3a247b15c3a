from pathlib import Path
from abc import ABC, abstractmethod

from ..config import config


class Uploader(ABC):
    @abstractmethod
    def upload(self, file_path: Path) -> bool:
        """
        将文件上传到指定目标位置
        :param file_path: 本地文件路径
        :return: 成功与否
        """
        pass


class LocalUploader(Uploader):
    def __init__(self):
        self.target_dir = Path(config.storage_dir) / "upload"
        if not self.target_dir.exists():
            self.target_dir.mkdir()

    def upload(self, file_path: Path) -> bool:
        import shutil

        target_path = self.target_dir / file_path.name

        try:
            shutil.copy(file_path, target_path)
            return True
        except Exception as e:
            print(f"Upload failed: {e}")
            return False


uploader = LocalUploader()
