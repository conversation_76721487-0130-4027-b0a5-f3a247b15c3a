# 限值提取工作流配置文件

# API配置
api:
  openai:
    model: "gpt-4o-mini"
    timeout: 30
    max_retries: 3

# 工作流配置
workflow:
  timeout: 60
  verbose: true
  default_user_id: "test_user"

# 查询配置
queries:
  default:
    - "铅的限量标准是多少？"
    - "微生物指标的要求是什么？"
    - "产品的保质期是多长时间？"
    - "贮存条件有什么要求？"
    - "感官要求包括哪些项目？"
    - "食品添加剂的使用标准是什么？"
    - "净含量的检验方法是什么？"
    - "原辅料的质量要求有哪些？"

# 文件路径配置
paths:
  output_dir: "output"
  test_data_dir: "test/data"
  storage_dir: ".storage/upload"
  default_pdf: "data/Q 04A4788S.pdf"
  default_image: "test/llm-ocr-test/data/test/0.png"

# Excel配置
excel:
  filename_template: "limit_extract_results_{timestamp}.xlsx"
  column_widths:
    query: 30
    result: 50
    type: 15
    nutrition_name: 15
    nutrition_amount: 10
    nutrition_unit: 10
    nutrition_nrv: 10
    summary_item: 20
    summary_status: 15
    summary_desc: 25

# 营养成分配置
nutrition:
  default_serving_size: 100
  default_serving_unit: "g"
  skip_rows:
    - "份量"
    - "状态"
    - "错误信息"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
