#!/usr/bin/env python3
"""
FastAPI应用 - 限量提取工作流API
"""

import asyncio
import sys
import os
import json
import uuid
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional, AsyncGenerator
import logging
from datetime import datetime

from fastapi import FastAPI, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.limit_extract_workflow import LimitExtractWorkflow

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI()

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置
TEMP_DIR = Path("./temp")
TEMP_DIR.mkdir(exist_ok=True)


# 数据模型
class LimitExtractRequest(BaseModel):
    """限值提取请求模型"""

    user_id: str = Field(..., description="用户ID")
    queries: List[str] = Field(..., description="查询列表", min_items=1)
    pdf_filename: str = Field(..., description="PDF文件名（需要先上传）")
    image_filename: str = Field(..., description="图片文件名（需要先上传）")
    timeout: Optional[int] = Field(60, description="超时时间（秒）", ge=10, le=300)


class UploadResponse(BaseModel):
    """文件上传响应模型"""

    status: str = Field(..., description="上传状态")
    message: str = Field(..., description="状态消息")
    filename: str = Field(..., description="保存的文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    file_path: str = Field(..., description="文件保存路径")
    timestamp: str = Field(..., description="上传时间戳")


class HealthResponse(BaseModel):
    """健康检查响应模型"""

    status: str
    timestamp: str
    version: str


# 辅助函数
def get_user_temp_dir(user_id: str) -> Path:
    """获取用户临时目录"""
    user_dir = TEMP_DIR / user_id
    user_dir.mkdir(exist_ok=True)
    return user_dir


def save_uploaded_file(file: UploadFile, user_id: str) -> tuple[Path, int]:
    """保存上传的文件"""
    user_dir = get_user_temp_dir(user_id)

    # 生成唯一文件名
    file_extension = Path(file.filename).suffix if file.filename else ""
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = user_dir / unique_filename

    # 保存文件
    file_size = 0
    with open(file_path, "wb") as buffer:
        while chunk := file.file.read(8192):  # 8KB chunks
            buffer.write(chunk)
            file_size += len(chunk)

    return file_path, file_size


async def create_sse_response(
    generator: AsyncGenerator[str, None],
) -> StreamingResponse:
    """创建SSE响应"""

    async def event_stream():
        try:
            async for data in generator:
                yield f"data: {data}\n\n"
        except Exception as e:
            error_data = json.dumps(
                {
                    "type": "error",
                    "message": f"Stream error: {str(e)}",
                    "timestamp": datetime.now().isoformat(),
                }
            )
            yield f"data: {error_data}\n\n"
        finally:
            # 发送结束信号
            yield f"data: {json.dumps({'type': 'end'})}\n\n"

    return StreamingResponse(
        event_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        },
    )


@app.get("/", response_model=Dict[str, str])
async def root():
    """根路径"""
    return {
        "message": "限量提取工作流API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
    }


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return HealthResponse(
        status="healthy", timestamp=datetime.now().isoformat(), version="1.0.0"
    )


@app.post("/upload")
async def upload_file(
    user_id: str = Form(..., description="用户ID"),
    file: UploadFile = File(..., description="要上传的文件"),
) -> UploadResponse:
    """
    文件上传接口

    - **user_id**: 用户ID，文件将保存到 ./temp/{user_id}/ 目录下
    - **file**: 要上传的文件（PDF或图片）
    """
    try:
        logger.info(f"用户 {user_id} 开始上传文件: {file.filename}")

        # 验证文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")

        # 验证文件类型
        allowed_extensions = {".pdf", ".png", ".jpg", ".jpeg", ".gif", ".bmp"}
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {file_extension}。支持的类型: {', '.join(allowed_extensions)}",
            )

        # 保存文件
        file_path, file_size = save_uploaded_file(file, user_id)

        logger.info(f"文件上传成功: {file_path}, 大小: {file_size} 字节")

        return UploadResponse(
            status="success",
            message="文件上传成功",
            filename=file_path.name,
            file_size=file_size,
            file_path=str(file_path.relative_to(TEMP_DIR)),
            timestamp=datetime.now().isoformat(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@app.post("/extract")
async def extract_limits_stream(request: LimitExtractRequest):
    """
    限值提取接口（SSE流式响应）

    - **user_id**: 用户ID
    - **queries**: 查询列表
    - **pdf_filename**: PDF文件名（需要先通过 /upload 上传）
    - **image_filename**: 图片文件名（需要先通过 /upload 上传）
    - **timeout**: 超时时间（秒，默认60秒）

    返回Server-Sent Events流，实时显示执行进度
    """

    async def workflow_stream_generator():
        try:
            # 发送开始消息
            yield json.dumps(
                {
                    "type": "start",
                    "message": f"开始执行限值提取工作流，用户: {request.user_id}",
                    "timestamp": datetime.now().isoformat(),
                }
            )

            # 验证文件路径
            user_dir = get_user_temp_dir(request.user_id)
            pdf_path = user_dir / request.pdf_filename
            image_path = user_dir / request.image_filename

            if not pdf_path.exists():
                raise FileNotFoundError(f"PDF文件不存在: {request.pdf_filename}")

            if not image_path.exists():
                raise FileNotFoundError(f"图片文件不存在: {request.image_filename}")

            yield json.dumps(
                {
                    "type": "progress",
                    "message": "文件验证完成，开始执行工作流",
                    "timestamp": datetime.now().isoformat(),
                }
            )

            # 准备工作流数据
            workflow_data = {
                "file_path": str(pdf_path),
                "image_path": str(image_path),
                "user_id": request.user_id,
                "queries": request.queries,
            }

            # 创建并运行工作流
            workflow = LimitExtractWorkflow(timeout=request.timeout, verbose=True)
            handler = workflow.run(data=workflow_data)

            # 流式处理工作流事件
            async for ev in handler.stream_events():
                # 这里可以根据不同的事件类型发送不同的进度信息
                event_data = {
                    "type": "workflow_event",
                    "event_type": type(ev).__name__,
                    "message": f"执行步骤: {type(ev).__name__}",
                    "timestamp": datetime.now().isoformat(),
                }
                yield json.dumps(event_data)

            # 获取最终结果
            final_result = await handler

            # 检查结果状态
            if "status" in final_result and final_result["status"] == "error":
                yield json.dumps(
                    {
                        "type": "error",
                        "message": f"工作流执行失败: {final_result.get('error_message', '未知错误')}",
                        "error_details": final_result,
                        "timestamp": datetime.now().isoformat(),
                    }
                )
            else:
                yield json.dumps(
                    {
                        "type": "success",
                        "message": "限值提取工作流执行成功",
                        "result": final_result,
                        "timestamp": datetime.now().isoformat(),
                    }
                )

        except FileNotFoundError as e:
            yield json.dumps(
                {
                    "type": "error",
                    "message": str(e),
                    "error_type": "file_not_found",
                    "timestamp": datetime.now().isoformat(),
                }
            )
        except Exception as e:
            logger.error(f"工作流执行失败: {e}")
            yield json.dumps(
                {
                    "type": "error",
                    "message": f"执行失败: {str(e)}",
                    "error_type": "execution_error",
                    "timestamp": datetime.now().isoformat(),
                }
            )

    return await create_sse_response(workflow_stream_generator())


if __name__ == "__main__":
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True, log_level="info")
