from pathlib import Path

import os

from magic_pdf.data.data_reader_writer import FileBasedDataWriter, FileBasedDataReader
from magic_pdf.data.dataset import PymuDocDataset
from magic_pdf.model.doc_analyze_by_custom_model import doc_analyze
from magic_pdf.config.enums import SupportedPdfParseMethod

from .base_converter import Converter
from ..config import config


class MinerUConverter(Converter):
    def __init__(self):
        self.storage_dir = config.storage_dir

    def _convert_pdf(self, pdf_file_name: Path) -> Path | None:
        name_without_suff = pdf_file_name.stem
        local_image_dir = Path(self.storage_dir) / "convert/images"
        local_md_dir = Path(self.storage_dir) / "convert"
        if not local_image_dir.exists():
            local_image_dir.mkdir(parents=True)
        local_image_dir, local_md_dir = str(local_image_dir), str(local_md_dir)
        image_dir = str(os.path.basename(local_image_dir))
        os.makedirs(local_image_dir, exist_ok=True)
        image_writer, md_writer = (
            FileBasedDataWriter(local_image_dir),
            FileBasedDataWriter(local_md_dir),
        )
        reader1 = FileBasedDataReader("")
        pdf_bytes = reader1.read(str(pdf_file_name))  # read the pdf content
        ds = PymuDocDataset(pdf_bytes)

        if ds.classify() == SupportedPdfParseMethod.OCR:
            infer_result = ds.apply(doc_analyze, ocr=True)
            pipe_result = infer_result.pipe_ocr_mode(image_writer)
        else:
            infer_result = ds.apply(doc_analyze, ocr=False)
            pipe_result = infer_result.pipe_txt_mode(image_writer)

        pipe_result.dump_md(md_writer, f"{name_without_suff}.md", image_dir)
        md_file_path = local_md_dir / Path(f"{name_without_suff}.md")
        return md_file_path

    def convert(self, file_path: Path) -> Path | None:
        if file_path.suffix == ".pdf":
            md_file_path = self._convert_pdf(file_path)
            return md_file_path
        else:
            return None


mineru_converter = MinerUConverter()
