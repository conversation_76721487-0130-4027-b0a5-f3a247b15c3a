from pathlib import Path

from .hash import HashCalculator, HashStore
from .hash import SHA256HashCalculator, MD5HashCalculator
from .hash import InMemoryHashStore, FileBasedHashStore


class DuplicateFileChecker:
    def __init__(self, hash_calculator: HashCalculator, hash_store: HashStore):
        self.hash_calculator = hash_calculator
        self.hash_store = hash_store

    def check(self, file_path: Path) -> tuple[bool, str]:
        file_hash = self.hash_calculator.calculate_hash(file_path)
        if self.hash_store.has_hash(file_hash):
            return True, file_hash
        else:
            self.hash_store.add_hash(file_hash)
            return False, file_hash


duplicate_file_checker = DuplicateFileChecker(
    hash_calculator=SHA256HashCalculator(),
    hash_store=FileBasedHashStore(),
)
