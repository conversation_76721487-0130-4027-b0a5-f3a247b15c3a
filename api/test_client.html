<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>限值提取工作流API - 控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 30px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .header h1 {
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }

        .header .subtitle {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 80px);
            gap: 0;
        }

        .left-panel {
            width: 45%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            overflow-y: auto;
            border-right: 1px solid rgba(0,0,0,0.1);
        }

        .right-panel {
            width: 55%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 30px;
            overflow-y: auto;
            color: white;
        }
<body>
    <h1>限值提取工作流API测试客户端</h1>
    
    <!-- 文件上传部分 -->
    <div class="container">
        <h2>1. 文件上传</h2>
        <div class="form-group">
            <label for="userId">用户ID:</label>
            <input type="text" id="userId" value="test_user_web" placeholder="输入用户ID">
        </div>
        
        <div class="form-group">
            <label for="pdfFile">PDF文件:</label>
            <input type="file" id="pdfFile" accept=".pdf">
            <div id="pdfUploadResult" class="upload-result" style="display: none;"></div>
        </div>
        
        <div class="form-group">
            <label for="imageFile">图片文件:</label>
            <input type="file" id="imageFile" accept=".png,.jpg,.jpeg,.gif,.bmp">
            <div id="imageUploadResult" class="upload-result" style="display: none;"></div>
        </div>
        
        <button onclick="uploadFiles()">上传文件</button>
    </div>
    
    <!-- 限值提取部分 -->
    <div class="container">
        <h2>2. 限值提取</h2>
        <div class="form-group">
            <label for="queries">查询列表 (每行一个查询):</label>
            <textarea id="queries" rows="4" placeholder="铅的限量标准是多少？&#10;微生物指标的要求是什么？&#10;产品的保质期是多长时间？">铅的限量标准是多少？
微生物指标的要求是什么？
产品的保质期是多长时间？</textarea>
        </div>
        
        <div class="form-group">
            <label for="timeout">超时时间（秒）:</label>
            <input type="number" id="timeout" value="60" min="10" max="300">
        </div>
        
        <button onclick="startExtraction()" id="extractBtn">开始限值提取</button>
    </div>
    
    <!-- 实时日志部分 -->
    <div class="container">
        <h2>3. 实时执行日志</h2>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let pdfFilename = null;
        let imageFilename = null;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function uploadFile(file, userId, resultElementId) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('user_id', userId);
            
            try {
                const response = await fetch(`${API_BASE}/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                const resultElement = document.getElementById(resultElementId);
                
                if (response.ok) {
                    resultElement.innerHTML = `
                        <strong>上传成功!</strong><br>
                        文件名: ${result.filename}<br>
                        大小: ${(result.file_size / 1024).toFixed(2)} KB<br>
                        路径: ${result.file_path}
                    `;
                    resultElement.style.display = 'block';
                    log(`文件上传成功: ${result.filename}`, 'success');
                    return result.filename;
                } else {
                    resultElement.innerHTML = `<strong>上传失败:</strong> ${result.detail}`;
                    resultElement.style.display = 'block';
                    log(`文件上传失败: ${result.detail}`, 'error');
                    return null;
                }
            } catch (error) {
                log(`上传请求失败: ${error.message}`, 'error');
                return null;
            }
        }
        
        async function uploadFiles() {
            const userId = document.getElementById('userId').value;
            const pdfFile = document.getElementById('pdfFile').files[0];
            const imageFile = document.getElementById('imageFile').files[0];
            
            if (!userId) {
                alert('请输入用户ID');
                return;
            }
            
            if (!pdfFile || !imageFile) {
                alert('请选择PDF文件和图片文件');
                return;
            }
            
            log('开始上传文件...', 'info');
            
            // 上传PDF文件
            pdfFilename = await uploadFile(pdfFile, userId, 'pdfUploadResult');
            
            // 上传图片文件
            imageFilename = await uploadFile(imageFile, userId, 'imageUploadResult');
            
            if (pdfFilename && imageFilename) {
                log('所有文件上传完成，可以开始限值提取', 'success');
            }
        }
        
        async function startExtraction() {
            if (!pdfFilename || !imageFilename) {
                alert('请先上传PDF文件和图片文件');
                return;
            }
            
            const userId = document.getElementById('userId').value;
            const queriesText = document.getElementById('queries').value;
            const timeout = parseInt(document.getElementById('timeout').value);
            
            const queries = queriesText.split('\n').filter(q => q.trim()).map(q => q.trim());
            
            if (queries.length === 0) {
                alert('请输入至少一个查询');
                return;
            }
            
            const requestData = {
                user_id: userId,
                queries: queries,
                pdf_filename: pdfFilename,
                image_filename: imageFilename,
                timeout: timeout
            };
            
            log('开始限值提取...', 'info');
            log(`请求数据: ${JSON.stringify(requestData, null, 2)}`, 'info');
            
            const extractBtn = document.getElementById('extractBtn');
            extractBtn.disabled = true;
            extractBtn.textContent = '执行中...';
            
            try {
                const response = await fetch(`${API_BASE}/extract`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const dataStr = line.slice(6);
                            if (dataStr.trim()) {
                                try {
                                    const eventData = JSON.parse(dataStr);
                                    const eventType = eventData.type || 'unknown';
                                    const message = eventData.message || '';
                                    
                                    let logType = 'info';
                                    if (eventType === 'error') logType = 'error';
                                    else if (eventType === 'success') logType = 'success';
                                    else if (eventType === 'workflow_event') logType = 'warning';
                                    
                                    log(`[${eventType}] ${message}`, logType);
                                    
                                    if (eventType === 'success') {
                                        const result = eventData.result || {};
                                        if (result.excel_file) {
                                            log(`Excel文件生成: ${result.excel_file}`, 'success');
                                        }
                                    }
                                    
                                    if (eventType === 'end') {
                                        log('执行完成', 'success');
                                        break;
                                    }
                                } catch (e) {
                                    log(`解析事件数据失败: ${dataStr}`, 'error');
                                }
                            }
                        }
                    }
                }
                
            } catch (error) {
                log(`执行失败: ${error.message}`, 'error');
            } finally {
                extractBtn.disabled = false;
                extractBtn.textContent = '开始限值提取';
            }
        }
    </script>
</body>
</html>
