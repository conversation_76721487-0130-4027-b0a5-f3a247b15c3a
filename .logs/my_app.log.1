2025-07-20 23:20:06,852 - watchfiles.main - DEBUG - 1045 changes detected: {(<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_logistic.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/speech_to_text_2/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/plaintext.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/nombank.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/logic.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/textcat.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/bnc.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/isri.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/sonority_sequencing.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/models.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/tagged.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/wordlist.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/kmeans.cpython-311.pyc.140429480513712'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/senna.cpython-311.pyc.140429496501392'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_theil_sen.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/image.cpython-311.pyc.140429495785712'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yoso/__pycache__/configuration_yoso.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/immutable.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/logic.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/porter.cpython-311.pyc.140429481078704'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_logistic.cpython-311.pyc.140429494197696'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_huber.cpython-311.pyc.140429494197168'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/em.cpython-311.pyc.140429480735440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba2/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ribes_score.cpython-311.pyc.140429485422736'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2_conformer/__pycache__/configuration_wav2vec2_conformer.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/util.cpython-311.pyc.140429495753168'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/senseval.cpython-311.pyc.140429483255120'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba/__pycache__/configuration_zamba.cpython-311.pyc.140429501044976'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/api.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio4.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/regexp.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/streams.cpython-311.pyc.*********781312'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/util.cpython-311.pyc.140429497763920'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio5_utils.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/smoothing.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/util.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/pchart.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/treebank.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/data.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/toolbox.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/nist_score.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/comparative_sents.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/association.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/arff/__pycache__/arffread.cpython-311.pyc.*************72'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/probability.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/internals.cpython-311.pyc.140429501226544'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/whisper/__pycache__/__init__.cpython-311.pyc.140429501055024'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/senna.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_california_housing.cpython-311.pyc.*********687600'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/svm/__pycache__/_classes.cpython-311.pyc.140429495440592'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/mmio.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/dependencygraph.cpython-311.pyc.140429494527136'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2/__pycache__/configuration_wav2vec2.cpython-311.pyc.140429500896800'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio5_utils.cpython-311.pyc.140429484539952'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_fortran.cpython-311.pyc.140429484426144'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/regexp.cpython-311.pyc.140429484438112'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/rte.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_idl.cpython-311.pyc.140429484649616'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/util.cpython-311.pyc.140429495785888'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/stanford_segmenter.cpython-311.pyc.140429497231376'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2/__pycache__/configuration_wav2vec2.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/decisiontree.cpython-311.pyc.140429495781136'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xmod/__pycache__/configuration_xmod.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/erroranalysis.cpython-311.pyc.140429495794864'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_kddcup99.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/projectivedependencyparser.cpython-311.pyc.140429494723824'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio.cpython-311.pyc.*********773568'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_harwell_boeing/__pycache__/hb.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm1.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_plot.cpython-311.pyc.140429495449568'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yolos/__pycache__/configuration_yolos.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/bllip.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/malt.cpython-311.pyc.140429495292816'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/combinator.cpython-311.pyc.140429480522336'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_sag.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/arlstem.cpython-311.pyc.140429480532384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/relextract.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/agreement.cpython-311.pyc.140429500362304'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/snowball.cpython-311.pyc.140429480533792'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/xmldocs.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/collections.cpython-311.pyc.140429497764880'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_classification_threshold.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/hmm.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/models.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ipipan.cpython-311.pyc.140429481773520'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta_xl/__pycache__/__init__.cpython-311.pyc.140429501041904'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/externals/__pycache__/_arff.cpython-311.pyc.140429484432304'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_bayes.cpython-311.pyc.140429495440240'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/svm/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/nonprojectivedependencyparser.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/meteor_score.cpython-311.pyc.140429485423264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/agreement.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/dependency.cpython-311.pyc.140429481774048'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/gale_church.cpython-311.pyc.140429480510544'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/wsd.cpython-311.pyc.140429479191344'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba/__pycache__/__init__.cpython-311.pyc.140429501044592'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/wordnet.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_logistic.cpython-311.pyc.140429494197696'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/toolbox.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/arff/__pycache__/_arffread.cpython-311.pyc.140429484430720'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/arlstem2.cpython-311.pyc.140429480532384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/chasen.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/chasen.cpython-311.pyc.140429481778272'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_harwell_boeing/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/feature.cpython-311.pyc.140429496343152'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/miobase.cpython-311.pyc.*********780784'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/resolution.cpython-311.pyc.140429485409008'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/repp.cpython-311.pyc.140429497229440'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/_dict_vectorizer.cpython-311.pyc.140429496031088'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/senseval.cpython-311.pyc.140429483255120'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/api.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio5.cpython-311.pyc.*********774624'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/mace.cpython-311.pyc.140429485410240'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/util.cpython-311.pyc.140429497226272'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/evaluate.cpython-311.pyc.140429494531008'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/chrf_score.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/downloader.cpython-311.pyc.140429480734320'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/stanford.cpython-311.pyc.140429495794512'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/verbnet.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/__pycache__/util.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/nkjp.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/svm/__pycache__/_bounds.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_arff_parser.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yolos/__pycache__/__init__.cpython-311.pyc.140429501043440'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/featurechart.cpython-311.pyc.140429494526608'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/wordlist.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_perceptron.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/pros_cons.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/text.cpython-311.pyc.140429495782720'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/help.cpython-311.pyc.140429478570512'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/switchboard.cpython-311.pyc.140429481775984'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/harwell_boeing.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_classification_threshold.cpython-311.pyc.140429495220496'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/lexicon.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/mmio.cpython-311.pyc.140429484657200'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2_bert/__pycache__/configuration_wav2vec2_bert.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/udhr.cpython-311.pyc.140429481783200'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/glue.cpython-311.pyc.140429482662672'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlnet/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/perceptron.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/phrase_based.cpython-311.pyc.140429480513008'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/_stop_words.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_ridge.cpython-311.pyc.*********768992'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/wordfinder.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/toolbox.cpython-311.pyc.140429483517840'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/dependency.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/cistem.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/wsd.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/linearlogic.cpython-311.pyc.140429485413408'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/tadm.cpython-311.pyc.140429495782544'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/perceptron.cpython-311.pyc.140429494519040'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio_utils.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/shiftreduce.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/bleu_score.cpython-311.pyc.140429485422736'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/positivenaivebayes.cpython-311.pyc.140429495782896'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/prover9.cpython-311.pyc.140429485410768'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/babelfish.cpython-311.pyc.140429480524976'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/api.cpython-311.pyc.140429483246496'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/utils/__pycache__/optimize.cpython-311.pyc.140429494195760'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/bllip.cpython-311.pyc.140429494768528'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/tree.cpython-311.pyc.140429496511472'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/chomsky.cpython-311.pyc.140429480521808'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio5_params.cpython-311.pyc.*********779904'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/internals.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/whisper/__pycache__/configuration_whisper.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_passive_aggressive.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/earleychart.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yoso/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/corenlp.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/util.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio_utils.cpython-311.pyc.*********780960'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/help.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/sentiwordnet.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/texttiling.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/dependencygraph.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_miobase.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/util.cpython-311.pyc.140429495750928'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_base.cpython-311.pyc.*********770928'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio5_params.cpython-311.pyc.*********780432'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/bracket_parse.cpython-311.pyc.140429483254416'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_split.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/crf.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/trocr/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba2/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/nist_score.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/named_entity.cpython-311.pyc.140429484437760'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/downloader.cpython-311.pyc.140429480734320'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/wordnet.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_quantile.cpython-311.pyc.*********766352'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/childes.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/featstruct.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/nombank.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/collections.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/x_clip/__pycache__/configuration_x_clip.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/childes.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wavlm/__pycache__/configuration_wavlm.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/string_category.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_openml.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/gleu_score.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/api.cpython-311.pyc.140429480736720'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/confusionmatrix.cpython-311.pyc.140429497930432'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/regexp.cpython-311.pyc.140429481198256'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/rte.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/_loss/__pycache__/loss.cpython-311.pyc.140429494192944'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/regexp.cpython-311.pyc.140429497229440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/transforms.cpython-311.pyc.140429494523968'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/switchboard.cpython-311.pyc.140429481775984'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/simple.cpython-311.pyc.140429497231904'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/sexpr.cpython-311.pyc.140429497234016'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/combinator.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/mte.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_fortran.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_passive_aggressive.cpython-311.pyc.140429494315760'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/api.cpython-311.pyc.140429497226096'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_twenty_newsgroups.cpython-311.pyc.*********991344'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba/__pycache__/configuration_zamba.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/recursivedescent.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/reviews.cpython-311.pyc.140429480529040'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/prover9.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/switchboard.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/gdfa.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm5.cpython-311.pyc.140429485418512'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/chart.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_theil_sen.cpython-311.pyc.*********771280'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_rcv1.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/collocations.cpython-311.pyc.140429500363360'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/mapping.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/feature.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/counter.cpython-311.pyc.140429497195440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/weka.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_olivetti_faces.cpython-311.pyc.*********773744'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/x_clip/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/viterbi.cpython-311.pyc.140429484438464'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/segmentation.cpython-311.pyc.140429497927264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlnet/__pycache__/configuration_xlnet.cpython-311.pyc.140429501043056'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/cmudict.cpython-311.pyc.140429483251952'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/lin.cpython-311.pyc.140429481779152'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/rte_classify.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ycoe.cpython-311.pyc.140429483258112'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/api.cpython-311.pyc.140429483246496'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/arff/__pycache__/__init__.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/api.cpython-311.pyc.140429495781136'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/chomsky.cpython-311.pyc.140429480521808'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/bnc.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wavlm/__pycache__/configuration_wavlm.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/vocabulary.cpython-311.pyc.140429497221696'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/regexp.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/senna.cpython-311.pyc.140429496501392'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/util.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/image.cpython-311.pyc.140429495785712'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/nps_chat.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/logic.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/porter.cpython-311.pyc.140429481078704'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_plot.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/util.cpython-311.pyc.140429495750928'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/em.cpython-311.pyc.140429480735440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/knbc.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_ridge.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/texttiling.cpython-311.pyc.140429497231552'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ieer.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/util.cpython-311.pyc.140429495753168'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba/__pycache__/configuration_zamba.cpython-311.pyc.140429501044976'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/toktok.cpython-311.pyc.140429497234544'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/comparative_sents.cpython-311.pyc.140429482173296'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm1.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/util.cpython-311.pyc.140429497763920'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm5.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/megam.cpython-311.pyc.140429495780256'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/models.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/util.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/arff/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xglm/__pycache__/configuration_xglm.cpython-311.pyc.140429501040752'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/api.cpython-311.pyc.140429478571792'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/panlex_swadesh.cpython-311.pyc.140429482174448'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/toolbox.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/nist_score.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/_loss/__pycache__/link.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/arff/__pycache__/arffread.cpython-311.pyc.*************72'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2_conformer/__pycache__/configuration_wav2vec2_conformer.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wavlm/__pycache__/configuration_wavlm.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/metrics.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/propbank.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/rte.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/gleu_score.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/babelfish.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/pchart.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/brill.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio5_utils.cpython-311.pyc.140429484539952'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/simple.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio5.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_fortran.cpython-311.pyc.140429484426144'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/aline.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2/__pycache__/configuration_wav2vec2.cpython-311.pyc.140429500896800'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_species_distributions.cpython-311.pyc.*********990576'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/_hash.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm3.cpython-311.pyc.140429485418512'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta/__pycache__/__init__.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/gaac.cpython-311.pyc.140429480513712'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/nonprojectivedependencyparser.cpython-311.pyc.140429494722480'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/stanford_segmenter.cpython-311.pyc.140429497231376'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/decisiontree.cpython-311.pyc.140429495781136'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/util.cpython-311.pyc.140429481205616'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/naivebayes.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xglm/__pycache__/configuration_xglm.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/trocr/__pycache__/configuration_trocr.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/prettyprinter.cpython-311.pyc.140429494523792'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/sequential.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_search.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/senseval.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/skolemize.cpython-311.pyc.140429497731888'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/crubadan.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/maxent.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/combinator.cpython-311.pyc.140429480522336'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/viterbi.cpython-311.pyc.140429484438464'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlnet/__pycache__/configuration_xlnet.cpython-311.pyc.140429501043056'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_species_distributions.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/bracket_parse.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/grammar.cpython-311.pyc.140429496910512'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba2/__pycache__/__init__.cpython-311.pyc.140429501043632'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/util.cpython-311.pyc.140429480513712'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_rcv1.cpython-311.pyc.140429484432304'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/rule.cpython-311.pyc.140429496343792'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/bleu_score.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xmod/__pycache__/configuration_xmod.cpython-311.pyc.140429501043440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/collections.cpython-311.pyc.140429497764880'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_coordinate_descent.cpython-311.pyc.140429493509296'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/opinion_lexicon.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_olivetti_faces.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/tableau.cpython-311.pyc.140429485409008'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/xlm_prophetnet/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/externals/__pycache__/_arff.cpython-311.pyc.140429484432304'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_bayes.cpython-311.pyc.140429495440240'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/idl.cpython-311.pyc.140429484820560'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/_glm/__pycache__/_newton_solver.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/x_clip/__pycache__/configuration_x_clip.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/arlstem2.cpython-311.pyc.140429480532384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/chasen.cpython-311.pyc.140429481778272'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/repp.cpython-311.pyc.140429497229440'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/probability.cpython-311.pyc.140429499108848'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm4.cpython-311.pyc.140429485418512'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/feature.cpython-311.pyc.140429496343152'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/miobase.cpython-311.pyc.*********780784'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/toktok.cpython-311.pyc.140429497234544'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_validation.cpython-311.pyc.140429493513520'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/_dict_vectorizer.cpython-311.pyc.140429496031088'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/rte_classify.cpython-311.pyc.140429495785888'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/brill_trainer.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/textcat.cpython-311.pyc.140429495788000'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/api.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/hmm.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/paice.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/propbank.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/whisper/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/__pycache__/util.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/externals/__pycache__/_arff.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/nkjp.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/util.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/svm/__pycache__/_bounds.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/featurechart.cpython-311.pyc.140429494526608'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yolos/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/wavfile.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/gaac.cpython-311.pyc.140429480513712'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/rule.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba2/__pycache__/configuration_zamba2.cpython-311.pyc.140429501045360'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/_loss/__pycache__/__init__.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/text.cpython-311.pyc.140429495782720'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/confusionmatrix.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/chart.cpython-311.pyc.140429494771248'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/mte.cpython-311.pyc.140429480526400'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2_bert/__pycache__/configuration_wav2vec2_bert.cpython-311.pyc.140430511434768'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_byteordercodes.cpython-311.pyc.*********775152'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_classification_threshold.cpython-311.pyc.140429495220496'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/mmio.cpython-311.pyc.140429484657200'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/vocabulary.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/glue.cpython-311.pyc.140429482662672'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/megam.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/transitionparser.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/__pycache__/util.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2_conformer/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/speech_to_text_2/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/phrase_based.cpython-311.pyc.140429480513008'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/gdfa.cpython-311.pyc.140429480513184'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_ridge.cpython-311.pyc.*********768992'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/wordnet.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/api.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/tnt.cpython-311.pyc.140429496418992'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/toolbox.cpython-311.pyc.140429483517840'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_miobase.cpython-311.pyc.*********774800'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/whisper/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/cistem.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/shiftreduce.cpython-311.pyc.140429495435312'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/svm/__pycache__/_classes.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/isri.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/linearlogic.cpython-311.pyc.140429485413408'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/gaac.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/rule.cpython-311.pyc.140429496343792'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/legality_principle.cpython-311.pyc.140429497229264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/perceptron.cpython-311.pyc.140429494519040'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/util.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/bleu_score.cpython-311.pyc.140429485422736'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio4.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/positivenaivebayes.cpython-311.pyc.140429495782896'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/prover9.cpython-311.pyc.140429485410768'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/decorators.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/bllip.cpython-311.pyc.140429494768528'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_harwell_boeing/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/framenet.cpython-311.pyc.140429481780384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/parsing.cpython-311.pyc.140429494524320'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/util.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yolos/__pycache__/configuration_yolos.cpython-311.pyc.140429501043824'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio5_params.cpython-311.pyc.*********779904'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/panlex_lite.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/earleychart.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/whisper/__pycache__/configuration_whisper.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/rslp.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/sinica_treebank.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm/__pycache__/configuration_xlm.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/rslp.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/utils/__pycache__/random.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_search.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/util.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio_utils.cpython-311.pyc.*********780960'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm4.cpython-311.pyc.140429485418512'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_base.cpython-311.pyc.*********770928'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/chomsky.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ppattach.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/prettyprinter.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/gleu_score.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/textcat.cpython-311.pyc.140429495788000'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/__init__.cpython-311.pyc.140429483245792'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/semcor.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/semcor.cpython-311.pyc.140429481780384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/arff/__pycache__/_arffread.cpython-311.pyc'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_harwell_boeing/__pycache__/__init__.cpython-311.pyc.*********983088'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/named_entity.cpython-311.pyc.140429484437760'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/wordnet.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/transforms.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/spearman.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/nombank.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/lexicon.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_least_angle.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/speech_to_text_2/__pycache__/__init__.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/childes.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/udhr.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/image.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/downloader.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/regexp.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wavlm/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/nougat/__pycache__/tokenization_nougat_fast.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/decorators.cpython-311.pyc.140429501233904'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/template.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/mte.cpython-311.pyc.140429480526400'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/chart.cpython-311.pyc.140429494771248'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/streams.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/sexpr.cpython-311.pyc.140429497234016'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_coordinate_descent.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_passive_aggressive.cpython-311.pyc.140429494315760'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/api.cpython-311.pyc.140429497226096'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/bcp47.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/tadm.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/arff/__pycache__/arffread.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/api.cpython-311.pyc.140429497196880'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_validation.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/speech_to_text_2/__pycache__/configuration_speech_to_text_2.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/wordnet.cpython-311.pyc.140429481772816'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm2.cpython-311.pyc.140429485418864'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_mmio.cpython-311.pyc.140429484641136'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm5.cpython-311.pyc.140429485418512'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_theil_sen.cpython-311.pyc.*********771280'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/scores.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/mwe.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/segmentation.cpython-311.pyc.140429497927264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ipipan.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/maxent.cpython-311.pyc.140429495781136'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_olivetti_faces.cpython-311.pyc.*********773744'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/stack_decoder.cpython-311.pyc.140429480510016'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/brill.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/x_clip/__pycache__/__init__.cpython-311.pyc.140429501039600'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ycoe.cpython-311.pyc.140429483258112'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/nkjp.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/xmldocs.cpython-311.pyc.140429483255120'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/tnt.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/spearman.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/cmudict.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/framenet.cpython-311.pyc.140429481780384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/api.cpython-311.pyc.140429495781136'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_omp.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/api.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/paice.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/parsing.cpython-311.pyc.140429494524320'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/vocabulary.cpython-311.pyc.140429497221696'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2_conformer/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/kmeans.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/punkt.cpython-311.pyc.140429497229440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/nps_chat.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/string_category.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/tagged.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xglm/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/toktok.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/casual.cpython-311.pyc.140429497224864'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/timit.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_kddcup99.cpython-311.pyc.*********772864'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/xlm_prophetnet/__pycache__/configuration_xlm_prophetnet.cpython-311.pyc.140429617300400'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/brill_trainer.cpython-311.pyc.140429495794864'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_ransac.cpython-311.pyc.*********766000'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/texttiling.cpython-311.pyc.140429497231552'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/chart.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/comparative_sents.cpython-311.pyc.140429482173296'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/boxer.cpython-311.pyc.140429497808528'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/_glm/__pycache__/glm.cpython-311.pyc.140429494190656'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/util.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xglm/__pycache__/configuration_xglm.cpython-311.pyc.140429501040752'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/megam.cpython-311.pyc.140429495780256'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm1.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/pl196x.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/probabilistic.cpython-311.pyc.140429494524672'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/api.cpython-311.pyc.140429478571792'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/panlex_swadesh.cpython-311.pyc.140429482174448'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/util.cpython-311.pyc.140429497198320'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/mwe.cpython-311.pyc.140429497229264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/harwell_boeing.cpython-311.pyc.140429484431600'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio5_params.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2_conformer/__pycache__/configuration_wav2vec2_conformer.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/pros_cons.cpython-311.pyc.140429480529040'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/destructive.cpython-311.pyc.140429497228208'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/nougat/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_netcdf.cpython-311.pyc.*********603536'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/smoothing.cpython-311.pyc.140429497222928'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/aligned.cpython-311.pyc.140429481777920'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/brill.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm3.cpython-311.pyc.140429485418512'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_species_distributions.cpython-311.pyc.*********990576'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/_loss/__pycache__/link.cpython-311.pyc.140429494195408'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/_glm/__pycache__/_newton_solver.cpython-311.pyc.140429494305776'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/nonprojectivedependencyparser.cpython-311.pyc.140429494722480'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/preprocessing.cpython-311.pyc.140429497222928'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta_xl/__pycache__/configuration_xlm_roberta_xl.cpython-311.pyc.140430360661088'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/stack_decoder.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/netcdf.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/nougat/__pycache__/tokenization_nougat_fast.cpython-311.pyc.140430141905872'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/util.cpython-311.pyc.140429481205616'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/preprocessing.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/text.cpython-311.pyc.140429496914192'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/sentiwordnet.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/api.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/prettyprinter.cpython-311.pyc.140429494523792'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm2.cpython-311.pyc.140429485418864'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio.cpython-311.pyc.*********779904'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/skolemize.cpython-311.pyc.140429497731888'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/weka.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/byteordercodes.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/utils/__pycache__/optimize.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm_model.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/jsontags.cpython-311.pyc.140429495749008'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/hunpos.cpython-311.pyc.140429496418992'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/glue.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/svm/__pycache__/_base.cpython-311.pyc.140429494195936'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/chart.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-311.pyc.*********983664'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/_loss/__pycache__/loss.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/viterbi.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm4.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/_stop_words.cpython-311.pyc.140429496030896'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_samples_generator.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/aline.cpython-311.pyc.140429500366176'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/recursivedescent.cpython-311.pyc.140429495435312'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/grammar.cpython-311.pyc.140429496910512'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/sequential.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/reviews.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/util.cpython-311.pyc.140429480513712'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/nps_chat.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_rcv1.cpython-311.pyc.140429484432304'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/casual.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/sonority_sequencing.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xmod/__pycache__/configuration_xmod.cpython-311.pyc.140429501043440'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/api.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/scikitlearn.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_coordinate_descent.cpython-311.pyc.140429493509296'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_split.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/util.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/drt.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_netcdf.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2_bert/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/distance.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/tableau.cpython-311.pyc.140429485409008'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/lfg.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/crubadan.cpython-311.pyc.140429480527104'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/xlm_prophetnet/__pycache__/__init__.cpython-311.pyc.140430370531536'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/boxer.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/lancaster.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/conll.cpython-311.pyc.140429483251776'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta/__pycache__/configuration_xlm_roberta.cpython-311.pyc.140430363444912'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/idl.cpython-311.pyc.140429484820560'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/api.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/api.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_ransac.cpython-311.pyc.*********766000'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/_dict_vectorizer.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/probability.cpython-311.pyc.140429499108848'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/parented.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_validation.cpython-311.pyc.140429493513520'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/rte_classify.cpython-311.pyc.140429495785888'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/probabilistic.cpython-311.pyc.140429494524672'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/sexpr.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/hunpos.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/twitter.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xglm/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/twitter.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/harwell_boeing.cpython-311.pyc.140429484431600'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm2.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/minimalset.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/netcdf.cpython-311.pyc.140429484657360'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/metrics.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/hmm.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/stanford_segmenter.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_stochastic_gradient.cpython-311.pyc.140429494315952'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_netcdf.cpython-311.pyc.*********603536'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/wavfile.cpython-311.pyc.140429484658160'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/util.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/verbnet.cpython-311.pyc.140429483259520'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/byteordercodes.cpython-311.pyc.*********781136'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/preprocessing.cpython-311.pyc.140429497222928'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba2/__pycache__/configuration_zamba2.cpython-311.pyc.140429501045360'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_linear_loss.cpython-311.pyc.140429494063472'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/decisiontree.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/wordfinder.cpython-311.pyc.140429480525152'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/lfg.cpython-311.pyc.140429498397552'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/discourse.cpython-311.pyc.140429485410416'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/bnc.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ieer.cpython-311.pyc.140429483255120'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2_bert/__pycache__/configuration_wav2vec2_bert.cpython-311.pyc.140430511434768'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/text.cpython-311.pyc.140429496914192'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/cistem.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_byteordercodes.cpython-311.pyc.*********775152'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/immutable.cpython-311.pyc.140429494520272'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_omp.cpython-311.pyc.140429494203856'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/jsontags.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/distance.cpython-311.pyc.140429500365648'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_perceptron.cpython-311.pyc.*********766352'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/wav2vec2_conformer/__pycache__/__init__.cpython-311.pyc.140429502799984'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/hunpos.cpython-311.pyc.140429496418992'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_covtype.cpython-311.pyc.*********769696'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/framenet.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/idl.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/pl196x.cpython-311.pyc.140429481777568'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/api.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/gdfa.cpython-311.pyc.140429480513184'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/utils/__pycache__/random.cpython-311.pyc.140429494191536'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_miobase.cpython-311.pyc.*********774800'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/tnt.cpython-311.pyc.140429496418992'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/positivenaivebayes.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/recursivedescent.cpython-311.pyc.140429495435312'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/arlstem2.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/shiftreduce.cpython-311.pyc.140429495435312'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_stochastic_gradient.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/chunked.cpython-311.pyc.140429483251776'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/trocr/__pycache__/configuration_trocr.cpython-311.pyc.140429500391920'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/legality_principle.cpython-311.pyc.140429497229264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_huber.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/api.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/minimalset.cpython-311.pyc.140429480525152'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yolos/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/aligned.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yoso/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_byteordercodes.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio5.cpython-311.pyc.*********779728'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_svmlight_format_io.cpython-311.pyc.*********988848'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/text.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/snowball.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/stanford.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yolos/__pycache__/configuration_yolos.cpython-311.pyc.140429501043824'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/parented.cpython-311.pyc.140429494521328'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/panlex_lite.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/projectivedependencyparser.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/arff/__pycache__/__init__.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/sinica_treebank.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm/__pycache__/configuration_xlm.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_lfw.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/rslp.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/earleychart.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/drt.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_search.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/treebank.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/logic.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta_xl/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_quantile.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xglm/__pycache__/__init__.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio5_params.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio4.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/util.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/legality_principle.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/semcor.cpython-311.pyc.140429481780384'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/knbc.cpython-311.pyc.140429481778448'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_samples_generator.cpython-311.pyc.*********990384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/erroranalysis.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_harwell_boeing/__pycache__/hb.cpython-311.pyc.140429484429488'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/spearman.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/lexicon.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/indian.cpython-311.pyc.140429483256880'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_least_angle.cpython-311.pyc.140429494063088'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ycoe.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/categorized_sents.cpython-311.pyc.140429482173296'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ppattach.cpython-311.pyc.140429483255120'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/decorators.cpython-311.pyc.140429501233904'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/arff/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_openml.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/evaluate.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio4.cpython-311.pyc.*********776384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xmod/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/evaluate.cpython-311.pyc.140429497729424'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba/__pycache__/__init__.cpython-311.pyc.140429501044592'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_sag.cpython-311.pyc.140429494203152'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/senna.cpython-311.pyc.140429495792224'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/api.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/immutable.cpython-311.pyc.140429494520272'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/plaintext.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/crf.cpython-311.pyc.140429496503632'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/api.cpython-311.pyc.140429497196880'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/xlm_prophetnet/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/corenlp.cpython-311.pyc.140429494529776'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/util.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/trocr/__pycache__/__init__.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/lazyimport.cpython-311.pyc.140429485426288'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/wordnet.cpython-311.pyc.140429481772816'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_mmio.cpython-311.pyc.140429484641136'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/probabilistic.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/scores.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/maxent.cpython-311.pyc.140429495781136'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/api.cpython-311.pyc.140429494768208'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_covtype.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/stack_decoder.cpython-311.pyc.140429480510016'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_svmlight_format_io.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/xmldocs.cpython-311.pyc.140429483255120'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_svmlight_format_io.cpython-311.pyc.*********988848'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio5.cpython-311.pyc.*********779728'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/_hash.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba2/__pycache__/__init__.cpython-311.pyc.140429501043632'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/paice.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlnet/__pycache__/configuration_xlnet.cpython-311.pyc'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/trocr/__pycache__/__init__.cpython-311.pyc.140429500391536'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/relextract.cpython-311.pyc.140429497729424'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/evaluate.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_linear_loss.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/punkt.cpython-311.pyc.140429497229440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/chunked.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/tagged.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/speech_to_text_2/__pycache__/configuration_speech_to_text_2.cpython-311.pyc.140429500585488'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta_xl/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/template.cpython-311.pyc.140429495787472'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/casual.cpython-311.pyc.140429497224864'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_kddcup99.cpython-311.pyc.*********772864'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/xlm_prophetnet/__pycache__/configuration_xlm_prophetnet.cpython-311.pyc.140429617300400'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/brill_trainer.cpython-311.pyc.140429495794864'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/lancaster.cpython-311.pyc.140429480533792'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta/__pycache__/configuration_xlm_roberta.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/destructive.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/boxer.cpython-311.pyc.140429497808528'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/_glm/__pycache__/glm.cpython-311.pyc.140429494190656'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm_model.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/opinion_lexicon.cpython-311.pyc.140429482172336'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/meteor_score.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/gale_church.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/util.cpython-311.pyc.140429497198320'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/mwe.cpython-311.pyc.140429497229264'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/mapping.cpython-311.pyc.140429496501392'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/bcp47.cpython-311.pyc.140429480530448'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_base.cpython-311.pyc.140429495443056'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/timit.cpython-311.pyc.140429483252304'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/pros_cons.cpython-311.pyc.140429480529040'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/destructive.cpython-311.pyc.140429497228208'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yoso/__pycache__/__init__.cpython-311.pyc.140429501043824'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/aligned.cpython-311.pyc.140429481777920'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/categorized_sents.cpython-311.pyc.140429482173296'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/smoothing.cpython-311.pyc.140429497222928'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/_loss/__pycache__/link.cpython-311.pyc.140429494195408'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/_glm/__pycache__/_newton_solver.cpython-311.pyc.140429494305776'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/indian.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta_xl/__pycache__/configuration_xlm_roberta_xl.cpython-311.pyc.140430360661088'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/_glm/__pycache__/glm.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/isri.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/nougat/__pycache__/tokenization_nougat_fast.cpython-311.pyc.140430141905872'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/wordlist.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/_loss/__pycache__/__init__.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/propbank.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/sentiwordnet.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio.cpython-311.pyc.*********779904'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__/chart.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/weka.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/scores.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm_model.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/punkt.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/jsontags.cpython-311.pyc.140429495749008'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/toolbox.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/svm/__pycache__/_base.cpython-311.pyc.140429494195936'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ribes_score.cpython-311.pyc.140429485422736'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/api.cpython-311.pyc.140429495750288'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-311.pyc.*********983664'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/_stop_words.cpython-311.pyc.140429496030896'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_idl.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ibm3.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/streams.cpython-311.pyc.*********781312'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/aline.cpython-311.pyc.140429500366176'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlnet/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/pchart.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/sequential.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/treebank.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/svm/__pycache__/_base.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/data.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/sonority_sequencing.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/api.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/association.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_split.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/internals.cpython-311.pyc.140429501226544'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_fast_matrix_market/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/svm/__pycache__/_classes.cpython-311.pyc.140429495440592'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_california_housing.cpython-311.pyc.*********687600'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/chrf_score.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/__init__.cpython-311.pyc.140429500363712'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/dependencygraph.cpython-311.pyc.140429494527136'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/crubadan.cpython-311.pyc.140429480527104'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yoso/__pycache__/configuration_yoso.cpython-311.pyc.140429501044592'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/regexp.cpython-311.pyc.140429484438112'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/tableau.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_idl.cpython-311.pyc.140429484649616'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/util.cpython-311.pyc.140429495785888'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/conll.cpython-311.pyc.140429483251776'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/panlex_lite.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta/__pycache__/configuration_xlm_roberta.cpython-311.pyc.140430363444912'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/util.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/erroranalysis.cpython-311.pyc.140429495794864'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/segmentation.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/ribes_score.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/trocr/__pycache__/__init__.cpython-311.pyc.140429500391536'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/projectivedependencyparser.cpython-311.pyc.140429494723824'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/conll.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/counter.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/whisper/__pycache__/configuration_whisper.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/api.cpython-311.pyc.140429484440224'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/scikitlearn.cpython-311.pyc.140429495784128'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/twitter.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/_loss/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/data.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/malt.cpython-311.pyc.140429495292816'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/metrics.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/netcdf.cpython-311.pyc.140429484657360'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/__init__.cpython-311.pyc.140429483245792'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_stochastic_gradient.cpython-311.pyc.140429494315952'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/arlstem.cpython-311.pyc.140429480532384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/wavfile.cpython-311.pyc.140429484658160'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/agreement.cpython-311.pyc.140429500362304'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_harwell_boeing/__pycache__/__init__.cpython-311.pyc.*********983088'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/verbnet.cpython-311.pyc.140429483259520'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/snowball.cpython-311.pyc.140429480533792'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/byteordercodes.cpython-311.pyc.*********781136'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/malt.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_linear_loss.cpython-311.pyc.140429494063472'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/api.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_ransac.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/wordfinder.cpython-311.pyc.140429480525152'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ipipan.cpython-311.pyc.140429481773520'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/lfg.cpython-311.pyc.140429498397552'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/lazyimport.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/discourse.cpython-311.pyc.140429485410416'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ieer.cpython-311.pyc.140429483255120'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/transitionparser.cpython-311.pyc.140429495435312'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_omp.cpython-311.pyc.140429494203856'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/meteor_score.cpython-311.pyc.140429485423264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/distance.cpython-311.pyc.140429500365648'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/dependency.cpython-311.pyc.140429481774048'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_perceptron.cpython-311.pyc.*********766352'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/gale_church.cpython-311.pyc.140429480510544'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/wsd.cpython-311.pyc.140429479191344'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/tree.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_covtype.cpython-311.pyc.*********769696'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_lfw.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/categorized_sents.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/grammar.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/pl196x.cpython-311.pyc.140429481777568'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/api.cpython-311.pyc.140429495750288'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/senna.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/zamba2/__pycache__/configuration_zamba2.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/api.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio5.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/utils/__pycache__/random.cpython-311.pyc.140429494191536'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/arff/__pycache__/_arffread.cpython-311.pyc.140429484430720'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/resolution.cpython-311.pyc.140429485409008'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/featurechart.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio5.cpython-311.pyc.*********774624'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/mace.cpython-311.pyc.140429485410240'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/evaluate.cpython-311.pyc.140429494531008'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/trocr/__pycache__/configuration_trocr.cpython-311.pyc.140429500391920'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/util.cpython-311.pyc.140429497226272'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/text.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/chunked.cpython-311.pyc.140429483251776'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/x_clip/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/naivebayes.cpython-311.pyc.140429495782896'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/api.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/minimalset.cpython-311.pyc.140429480525152'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/stanford.cpython-311.pyc.140429495794512'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_twenty_newsgroups.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/phrase_based.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/_glm/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/translate/__pycache__/chrf_score.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm/__pycache__/configuration_xlm.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/yoso/__pycache__/configuration_yoso.cpython-311.pyc.140429501044592'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/parented.cpython-311.pyc.140429494521328'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_base.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/api.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_lfw.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/drt.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/help.cpython-311.pyc.140429478570512'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/em.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/logic.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/miobase.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/udhr.cpython-311.pyc.140429481783200'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/api.cpython-311.pyc.140429484440224'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_base.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/scikitlearn.cpython-311.pyc.140429495784128'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio4.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/knbc.cpython-311.pyc.140429481778448'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_samples_generator.cpython-311.pyc.*********990384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/featstruct.cpython-311.pyc.140429497805808'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_arff_parser.cpython-311.pyc.140429484433184'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/_harwell_boeing/__pycache__/hb.cpython-311.pyc.140429484429488'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/util.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/indian.cpython-311.pyc.140429483256880'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_least_angle.cpython-311.pyc.140429494063088'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/xlm_prophetnet/__pycache__/configuration_xlm_prophetnet.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/discourse.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/util.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/tadm.cpython-311.pyc.140429495782544'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/ccg/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/util.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/ppattach.cpython-311.pyc.140429483255120'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/arlstem.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/plaintext.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/babelfish.cpython-311.pyc.140429480524976'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/resolution.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio4.cpython-311.pyc.*********776384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/evaluate.cpython-311.pyc.140429497729424'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/senna.cpython-311.pyc.140429495792224'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/utils/__pycache__/optimize.cpython-311.pyc.140429494195760'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_bayes.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/tree.cpython-311.pyc.140429496511472'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_sag.cpython-311.pyc.140429494203152'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/transitionparser.cpython-311.pyc.140429495435312'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/porter.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/crf.cpython-311.pyc.140429496503632'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/kmeans.cpython-311.pyc.140429480513712'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/panlex_swadesh.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/corenlp.cpython-311.pyc.140429494529776'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/lazyimport.cpython-311.pyc.140429485426288'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/lin.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/_loss/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/logic.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_huber.cpython-311.pyc.140429494197168'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/mio5_params.cpython-311.pyc.*********780432'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/svm/__pycache__/_bounds.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/bracket_parse.cpython-311.pyc.140429483254416'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/parse/__pycache__/api.cpython-311.pyc.140429494768208'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/classify/__pycache__/naivebayes.cpython-311.pyc.140429495782896'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/__pycache__/_mmio.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_quantile.cpython-311.pyc.*********766352'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/linearlogic.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/__init__.cpython-311.pyc.*********774096'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/sinica_treebank.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/x_clip/__pycache__/configuration_x_clip.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/feature_extraction/__pycache__/_hash.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/string_category.cpython-311.pyc.***************'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_openml.cpython-311.pyc.***************'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/skolemize.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/regexp.cpython-311.pyc.140429481198256'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/confusionmatrix.cpython-311.pyc.140429497930432'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/api.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/_loss/__pycache__/loss.cpython-311.pyc.140429494192944'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/sem/__pycache__/relextract.cpython-311.pyc.140429497729424'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/cluster/__pycache__/api.cpython-311.pyc.140429480736720'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_california_housing.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/regexp.cpython-311.pyc.140429497229440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/inference/__pycache__/mace.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/repp.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/transforms.cpython-311.pyc.140429494523968'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/collocations.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta/__pycache__/__init__.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tokenize/__pycache__/simple.cpython-311.pyc.140429497231904'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta_xl/__pycache__/configuration_xlm_roberta_xl.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/deprecated/speech_to_text_2/__pycache__/configuration_speech_to_text_2.cpython-311.pyc.140429500585488'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tbl/__pycache__/template.cpython-311.pyc.140429495787472'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/stem/__pycache__/lancaster.cpython-311.pyc.140429480533792'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_twenty_newsgroups.cpython-311.pyc.*********991344'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tree/__pycache__/parsing.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/reviews.cpython-311.pyc.140429480529040'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/scipy/io/matlab/__pycache__/_mio.cpython-311.pyc.*********773568'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/misc/__pycache__/__init__.cpython-311.pyc.140429480521104'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/chunk/__pycache__/named_entity.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/featstruct.cpython-311.pyc.140429497805808'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/metrics/__pycache__/association.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/model_selection/__pycache__/_plot.cpython-311.pyc.140429495449568'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/opinion_lexicon.cpython-311.pyc.140429482172336'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/datasets/__pycache__/_arff_parser.cpython-311.pyc.140429484433184'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlnet/__pycache__/__init__.cpython-311.pyc.140429501042672'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/__pycache__/collocations.cpython-311.pyc.140429500363360'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/tag/__pycache__/mapping.cpython-311.pyc.140429496501392'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/bcp47.cpython-311.pyc.140429480530448'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/sklearn/linear_model/__pycache__/_base.cpython-311.pyc.140429495443056'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/timit.cpython-311.pyc.140429483252304'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/cmudict.cpython-311.pyc.140429483251952'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/xlm_roberta/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/corpus/reader/__pycache__/lin.cpython-311.pyc.140429481779152'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/nltk/lm/__pycache__/counter.cpython-311.pyc.140429497195440')}
2025-07-20 23:20:07,332 - watchfiles.main - DEBUG - 8 changes detected: {(<Change.added: 1>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.3'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.2'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.2'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.3')}
2025-07-20 23:20:07,783 - watchfiles.main - DEBUG - 354 changes detected: {(<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_version.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/LexerATNSimulator.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/pyclipper/__pycache__/_version.cpython-311.pyc.140429476918032'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/point.cpython-311.pyc.140429476917504'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_geometry.cpython-311.pyc.140429480523920'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Recognizer.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNDeserializer.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/linestring.cpython-311.pyc.140429476917152'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNConfig.cpython-311.pyc.140429469837856'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/resolvers/oc/__pycache__/dict.cpython-311.pyc.140429468952592'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/IntervalSet.cpython-311.pyc.140429469836800'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_svtrnet.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/necks/__pycache__/db_fpn.cpython-311.pyc.140429468941600'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_lcnetv3.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/algorithms/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/OmegaConfGrammarLexer.cpython-311.pyc.140429470064752'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/__pycache__/common.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/ErrorStrategy.cpython-311.pyc.140429469840144'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/necks/__pycache__/intracl.cpython-311.pyc.140429468435440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/collection.cpython-311.pyc.140429476915216'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/dfa/__pycache__/DFA.cpython-311.pyc.140429470128912'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/version.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_ragged_array.cpython-311.pyc.140429476905184'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/ParserRuleContext.cpython-311.pyc.140429469829760'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/BufferedTokenStream.cpython-311.pyc.140429469829408'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/RuleTagToken.cpython-311.pyc.140429470319344'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/constructive.cpython-311.pyc.140429476907648'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/LexerActionExecutor.cpython-311.pyc.140429469838208'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_geometry.cpython-311.pyc.140429480523920'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/dfa/__pycache__/DFA.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/postprocess/__pycache__/rec_postprocess.cpython-311.pyc.140429479144752'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/CommonTokenFactory.cpython-311.pyc.140429469834160'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/ParseTreePatternMatcher.cpython-311.pyc.140429470059952'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/predicates.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/set_operations.cpython-311.pyc.140429476909408'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/OmegaConfGrammarLexer.cpython-311.pyc.140429470064752'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ParserATNSimulator.cpython-311.pyc.140429470320048'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/cls_head.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/affinity.cpython-311.pyc.140429476994448'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/ErrorListener.cpython-311.pyc.140429469832576'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/coords.cpython-311.pyc.140429477023856'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/collection.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/linestring.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/grammar_parser.cpython-311.pyc.140429470323744'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Recognizer.cpython-311.pyc.140429469832224'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/det_db_head.cpython-311.pyc.140429468439040'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/Trees.cpython-311.pyc.140429469832224'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/Trees.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/geo.cpython-311.pyc.140429476916448'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/SemanticContext.cpython-311.pyc.140429469838736'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/io.cpython-311.pyc.140429478951344'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATN.cpython-311.pyc.140429469682864'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/base.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/linear.cpython-311.pyc.140429477017712'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/RuleContext.cpython-311.pyc.140429469830640'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/errors.cpython-311.pyc.140429470371792'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/rec_ctc_head.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/rec_multi_head.cpython-311.pyc.140429468439040'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNDeserializationOptions.cpython-311.pyc.140429470059760'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNState.cpython-311.pyc.140429469837328'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/OmegaConfGrammarParser.cpython-311.pyc.140429470067248'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/polygon.cpython-311.pyc.140429476918032'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/necks/__pycache__/rnn.cpython-311.pyc.140429468389136'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/DiagnosticErrorListener.cpython-311.pyc.140429470061872'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/FileStream.cpython-311.pyc.140429469828704'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/pyclipper/__pycache__/__init__.cpython-311.pyc.140429476919968'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/Chunk.cpython-311.pyc.140429470319344'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/omegaconf.cpython-311.pyc.140429468951360'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/errors.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/ErrorListener.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/dfa/__pycache__/DFAState.cpython-311.pyc.140429469839968'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/necks/__pycache__/db_fpn.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/errors.cpython-311.pyc.140429478939504'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/base.cpython-311.pyc.140429477214320'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/decorators.cpython-311.pyc.140429480524272'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/multipolygon.cpython-311.pyc'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/__init__.cpython-311.pyc.140429476912576'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/postprocess/__pycache__/cls_postprocess.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNDeserializer.cpython-311.pyc.140429469842080'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/resolvers/oc/__pycache__/dict.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/algorithms/__pycache__/_oriented_envelope.cpython-311.pyc.140429476890928'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/multipolygon.cpython-311.pyc.140429476917680'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/_utils.cpython-311.pyc.140429477215920'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_svtrnet.cpython-311.pyc.140429468434960'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/multipoint.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/resolvers/__pycache__/__init__.cpython-311.pyc.140429468953648'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/multilinestring.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/resolvers/oc/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/coordinates.cpython-311.pyc'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/grammar_parser.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/polygon.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/_utils.cpython-311.pyc'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/__init__.cpython-311.pyc.140429470324800'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/strtree.cpython-311.pyc.140429477020656'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/affinity.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/ListTokenSource.cpython-311.pyc.140429470318992'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNConfig.cpython-311.pyc.140429469837856'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/measurement.cpython-311.pyc'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/__init__.cpython-311.pyc.140429477215760'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/dfa/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/basecontainer.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Token.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/necks/__pycache__/intracl.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/postprocess/__pycache__/rec_postprocess.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/necks/__pycache__/intracl.cpython-311.pyc.140429468435440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/listconfig.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNState.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/resolvers/__pycache__/__init__.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/LexerAction.cpython-311.pyc.140429469839264'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/basecontainer.cpython-311.pyc.140429468949424'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/ParserRuleContext.cpython-311.pyc.140429469829760'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/constructive.cpython-311.pyc.140429476907648'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/det_db_head.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/omegaconf.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNSimulator.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_coverage.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/listconfig.cpython-311.pyc.140429468951536'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/pyclipper/__pycache__/__init__.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/PredictionContext.cpython-311.pyc.140429469834864'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/postprocess/__pycache__/db_postprocess.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/InputStream.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/ListTokenSource.cpython-311.pyc.140429470318992'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNType.cpython-311.pyc.140429469834688'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/PredictionMode.cpython-311.pyc.140429470321632'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/Chunk.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/grammar_visitor.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_version.cpython-311.pyc.140429477203760'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/ParseTreePatternMatcher.cpython-311.pyc.140429470059952'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/resolvers/oc/__pycache__/__init__.cpython-311.pyc.140429468953120'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/set_operations.cpython-311.pyc.140429476909408'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/__init__.cpython-311.pyc.140429469830816'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ParserATNSimulator.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/creation.cpython-311.pyc.140429478950384'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/grammar_visitor.cpython-311.pyc.140429470324448'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/affinity.cpython-311.pyc.140429476994448'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/ErrorListener.cpython-311.pyc.140429469832576'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/coords.cpython-311.pyc.140429477023856'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_enum.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/measurement.cpython-311.pyc.140429476909232'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/det_db_head.cpython-311.pyc.140429468439040'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/algorithms/__pycache__/cga.cpython-311.pyc.140429476908704'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/geo.cpython-311.pyc.140429476916448'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/SemanticContext.cpython-311.pyc.140429469838736'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATN.cpython-311.pyc.140429469682864'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/algorithms/__pycache__/cga.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/necks/__pycache__/rnn.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/__init__.cpython-311.pyc.140429469829936'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Utils.cpython-311.pyc.140429469753680'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/RuleContext.cpython-311.pyc.140429469830640'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/Errors.cpython-311.pyc.140429469829584'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNDeserializationOptions.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/listconfig.cpython-311.pyc.140429468951536'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/errors.cpython-311.pyc.140429470371792'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_ragged_array.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/rec_multi_head.cpython-311.pyc.140429468439040'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/polygon.cpython-311.pyc.140429476918032'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/strtree.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/base.cpython-311.pyc.140429476912400'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/dfa/__pycache__/DFAState.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/PredictionMode.cpython-311.pyc.140429470321632'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/DiagnosticErrorListener.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/Chunk.cpython-311.pyc.140429470319344'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/LexerATNSimulator.cpython-311.pyc.140429469834336'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/dfa/__pycache__/DFAState.cpython-311.pyc.140429469839968'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/base.cpython-311.pyc.140429477214320'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/multilinestring.cpython-311.pyc.140429476917680'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/version.cpython-311.pyc.140429468953296'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/Tree.cpython-311.pyc.140429469753520'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/Transition.cpython-311.pyc.140429469837680'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNDeserializer.cpython-311.pyc.140429469842080'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/nodes.cpython-311.pyc.140429469362384'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/postprocess/__pycache__/cls_postprocess.cpython-311.pyc.140429477109536'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNConfig.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/_utils.cpython-311.pyc.140429477215920'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_lcnetv3.cpython-311.pyc.140429468939680'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/constructive.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Parser.cpython-311.pyc.140429469750160'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/dfa/__pycache__/__init__.cpython-311.pyc.140429469839264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/ParserRuleContext.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/CommonTokenFactory.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/coordinates.cpython-311.pyc.140429476905360'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/OmegaConfGrammarParserVisitor.cpython-311.pyc.140429470016912'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/strtree.cpython-311.pyc.140429477020656'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/Tree.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/point.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/LexerAction.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/LexerActionExecutor.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_coverage.cpython-311.pyc.140429476919264'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_enum.cpython-311.pyc.140429478941104'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNType.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/geo.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/LexerAction.cpython-311.pyc.140429469839264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/basecontainer.cpython-311.pyc.140429468949424'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/resolvers/oc/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/det_mobilenet_v3.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/dictconfig.cpython-311.pyc.140429468948368'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/InputStream.cpython-311.pyc.140429469827648'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/__init__.cpython-311.pyc.140429469834160'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/IntervalSet.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Token.cpython-311.pyc.140429469745200'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/postprocess/__pycache__/db_postprocess.cpython-311.pyc.140429479138992'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/pyclipper/__pycache__/_version.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/PredictionContext.cpython-311.pyc.140429469834864'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATN.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNType.cpython-311.pyc.140429469834688'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/pyclipper/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/Transition.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/pyclipper/__pycache__/__init__.cpython-311.pyc.140429476919968'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_version.cpython-311.pyc.140429477203760'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/StdinStream.cpython-311.pyc.140429469828704'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/creation.cpython-311.pyc.140429478950384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/grammar_visitor.cpython-311.pyc.140429470324448'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/__init__.cpython-311.pyc.140429476912576'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/measurement.cpython-311.pyc.140429476909232'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/algorithms/__pycache__/cga.cpython-311.pyc.140429476908704'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/TokenTagToken.cpython-311.pyc.140429470318640'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/CommonTokenStream.cpython-311.pyc.140429469831168'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNConfigSet.cpython-311.pyc.140429469835744'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/SemanticContext.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/BufferedTokenStream.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Utils.cpython-311.pyc.140429469753680'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/RuleTagToken.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/Errors.cpython-311.pyc.140429469829584'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/base.cpython-311.pyc.140429476912400'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/rec_ctc_head.cpython-311.pyc.140429468439040'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/det_mobilenet_v3.cpython-311.pyc.140429468938240'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/LexerATNSimulator.cpython-311.pyc.140429469834336'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/__init__.cpython-311.pyc.140429470324800'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/decorators.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Lexer.cpython-311.pyc.140429469645392'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/OmegaConfGrammarParserVisitor.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/version.cpython-311.pyc.140429468953296'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/multilinestring.cpython-311.pyc.140429476917680'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/Tree.cpython-311.pyc.140429469753520'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/Transition.cpython-311.pyc.140429469837680'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/__init__.cpython-311.pyc.140429477215760'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/OmegaConfGrammarParser.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/__pycache__/__init__.cpython-311.pyc.140429470323568'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/nodes.cpython-311.pyc.140429469362384'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/postprocess/__pycache__/cls_postprocess.cpython-311.pyc.140429477109536'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/CommonTokenStream.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/errors.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/__pycache__/common.cpython-311.pyc.140429468822960'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_hgnet.cpython-311.pyc.140429468938960'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_lcnetv3.cpython-311.pyc.140429468939680'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Parser.cpython-311.pyc.140429469750160'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/coords.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Lexer.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/multipoint.cpython-311.pyc.140429476917680'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/coordinates.cpython-311.pyc.140429476905360'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/OmegaConfGrammarParserVisitor.cpython-311.pyc.140429470016912'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/ListTokenSource.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/set_operations.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/cls_head.cpython-311.pyc.140429468440720'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/__init__.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNSimulator.cpython-311.pyc.140429469837856'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/ParseTreePatternMatcher.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/__init__.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/__init__.cpython-311.pyc.140429469830816'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/pyclipper/__pycache__/_version.cpython-311.pyc.140429476918032'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/PredictionContext.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_coverage.cpython-311.pyc.140429476919264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_enum.cpython-311.pyc.140429478941104'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/__pycache__/common.cpython-311.pyc.140429468822960'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/point.cpython-311.pyc.140429476917504'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/nodes.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/linestring.cpython-311.pyc.140429476917152'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/resolvers/oc/__pycache__/dict.cpython-311.pyc.140429468952592'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/IntervalSet.cpython-311.pyc.140429469836800'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/dictconfig.cpython-311.pyc.140429468948368'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/multipoint.cpython-311.pyc.140429476917680'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/InputStream.cpython-311.pyc.140429469827648'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/necks/__pycache__/db_fpn.cpython-311.pyc.140429468941600'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/StdinStream.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/FileStream.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Utils.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Token.cpython-311.pyc.140429469745200'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/postprocess/__pycache__/db_postprocess.cpython-311.pyc.140429479138992'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/linear.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/collection.cpython-311.pyc.140429476915216'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/algorithms/__pycache__/_oriented_envelope.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/dfa/__pycache__/DFA.cpython-311.pyc.140429470128912'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/TokenTagToken.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_ragged_array.cpython-311.pyc.140429476905184'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/BufferedTokenStream.cpython-311.pyc.140429469829408'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_hgnet.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/StdinStream.cpython-311.pyc.140429469828704'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/predicates.cpython-311.pyc.140429476905888'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/LexerActionExecutor.cpython-311.pyc.140429469838208'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/postprocess/__pycache__/rec_postprocess.cpython-311.pyc.140429479144752'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/CommonTokenFactory.cpython-311.pyc.140429469834160'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/TokenTagToken.cpython-311.pyc.140429470318640'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/CommonTokenStream.cpython-311.pyc.140429469831168'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/_geometry.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNConfigSet.cpython-311.pyc.140429469835744'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/dfa/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/PredictionMode.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ParserATNSimulator.cpython-311.pyc.140429470320048'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/dfa/__pycache__/__init__.cpython-311.pyc.140429469839264'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/OmegaConfGrammarLexer.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNConfigSet.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/resolvers/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/rec_ctc_head.cpython-311.pyc.140429468439040'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/grammar_parser.cpython-311.pyc.140429470323744'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Recognizer.cpython-311.pyc.140429469832224'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/det_mobilenet_v3.cpython-311.pyc.140429468938240'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/Trees.cpython-311.pyc.140429469832224'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/base.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Lexer.cpython-311.pyc.140429469645392'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/io.cpython-311.pyc.140429478951344'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/linear.cpython-311.pyc.140429477017712'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/dictconfig.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/RuleContext.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/predicates.cpython-311.pyc.140429476905888'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/creation.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/Errors.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/Parser.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNDeserializationOptions.cpython-311.pyc.140429470059760'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNState.cpython-311.pyc.140429469837328'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/grammar/gen/__pycache__/OmegaConfGrammarParser.cpython-311.pyc.140429470067248'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_hgnet.cpython-311.pyc.140429468938960'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/necks/__pycache__/rnn.cpython-311.pyc.140429468389136'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/__pycache__/FileStream.cpython-311.pyc.140429469828704'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/DiagnosticErrorListener.cpython-311.pyc.140429470061872'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/__pycache__/omegaconf.cpython-311.pyc.140429468951360'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/omegaconf/resolvers/oc/__pycache__/__init__.cpython-311.pyc.140429468953120'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/errors.cpython-311.pyc.140429478939504'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/ErrorStrategy.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/decorators.cpython-311.pyc.140429480524272'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/__init__.cpython-311.pyc.140429469834160'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/rec_multi_head.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/error/__pycache__/ErrorStrategy.cpython-311.pyc.140429469840144'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/algorithms/__pycache__/_oriented_envelope.cpython-311.pyc.140429476890928'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/heads/__pycache__/cls_head.cpython-311.pyc.140429468440720'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/multipolygon.cpython-311.pyc.140429476917680'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_svtrnet.cpython-311.pyc.140429468434960'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/__pycache__/io.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/atn/__pycache__/ATNSimulator.cpython-311.pyc.140429469837856'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/shapely/geometry/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/antlr4/tree/__pycache__/RuleTagToken.cpython-311.pyc.140429470319344')}
2025-07-20 23:20:08,136 - watchfiles.main - DEBUG - 17 changes detected: {(<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_mv1_enhance.cpython-311.pyc.140429468441200'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_mobilenet_v3.cpython-311.pyc.140429468441200'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_pphgnetv2.cpython-311.pyc.140429468441440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/cpuinfo/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/doclayout_yolo/utils/callbacks/__pycache__/hub.cpython-311.pyc.140429462263664'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/cpuinfo/__pycache__/cpuinfo.cpython-311.pyc.140429464195984'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/doclayout_yolo/utils/callbacks/__pycache__/hub.cpython-311.pyc.140429462263664'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_pphgnetv2.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_pphgnetv2.cpython-311.pyc.140429468441440'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/cpuinfo/__pycache__/cpuinfo.cpython-311.pyc'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/cpuinfo/__pycache__/cpuinfo.cpython-311.pyc.140429464195984'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_mobilenet_v3.cpython-311.pyc.140429468441200'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_mv1_enhance.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/doclayout_yolo/utils/callbacks/__pycache__/hub.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_mv1_enhance.cpython-311.pyc.140429468441200'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/ocr/paddleocr2pytorch/pytorchocr/modeling/backbones/__pycache__/rec_mobilenet_v3.cpython-311.pyc')}
2025-07-20 23:20:08,487 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:08,838 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:09,238 - watchfiles.main - DEBUG - 4 changes detected: {(<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/doclayout_yolo/engine/__pycache__/exporter.cpython-311.pyc.140429461238016'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/doclayout_yolo/engine/__pycache__/exporter.cpython-311.pyc.140429461238016'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/doclayout_yolo/engine/__pycache__/exporter.cpython-311.pyc'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:09,589 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:09,940 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:10,290 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:10,641 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:10,992 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:11,343 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:11,693 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:12,044 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:12,395 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:12,745 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:13,097 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:13,448 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:13,800 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:14,150 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:14,501 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:14,852 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:15,202 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:15,553 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:15,904 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:16,255 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:16,605 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:16,956 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:17,307 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:17,658 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:18,008 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:18,359 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:18,710 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:19,060 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:19,411 - watchfiles.main - DEBUG - 10 changes detected: {(<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/ultralytics/utils/callbacks/__pycache__/hub.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/ultralytics/engine/__pycache__/exporter.cpython-311.pyc.140429461238192'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/ultralytics/utils/__pycache__/export.cpython-311.pyc.140429461235728'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/ultralytics/engine/__pycache__/exporter.cpython-311.pyc.140429461238192'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/ultralytics/utils/callbacks/__pycache__/hub.cpython-311.pyc.140429461238544'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/ultralytics/utils/__pycache__/export.cpython-311.pyc.140429461235728'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/ultralytics/utils/__pycache__/export.cpython-311.pyc'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/ultralytics/utils/callbacks/__pycache__/hub.cpython-311.pyc.140429461238544'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/ultralytics/engine/__pycache__/exporter.cpython-311.pyc')}
2025-07-20 23:20:19,762 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:20,112 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:20,463 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:20,814 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:21,165 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:21,516 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:21,867 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:22,218 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:22,569 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:22,920 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:23,270 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:23,621 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:23,972 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:24,323 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:24,674 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:25,025 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:25,376 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:25,727 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:26,077 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:26,428 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:26,779 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:27,130 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:27,481 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:27,832 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:28,183 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:28,534 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:28,885 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:29,236 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:29,587 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:29,938 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:30,289 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:30,640 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:30,990 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:31,341 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:31,692 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:32,043 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:32,394 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:32,744 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:33,095 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:33,446 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:33,796 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:34,147 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:34,498 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:34,849 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:35,200 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:35,551 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:35,902 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:36,253 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:36,604 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:36,954 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:37,305 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:37,656 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:38,007 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:38,358 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:38,708 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:39,059 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:39,410 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:39,760 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:40,111 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:40,462 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:40,812 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:41,163 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:41,513 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:41,864 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:42,215 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:42,566 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:42,917 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:43,267 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:43,618 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:43,969 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:44,320 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:44,671 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:45,022 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:45,372 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:45,723 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:46,074 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:46,425 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:46,776 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:47,127 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:47,478 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:47,829 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:48,179 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:48,530 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:48,881 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:49,232 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:49,583 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:49,934 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:50,285 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:50,636 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:50,987 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:51,337 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:51,688 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:52,039 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:52,390 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:52,741 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:53,091 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:53,442 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:53,793 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:54,144 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:54,495 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:54,846 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:55,196 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:55,547 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:55,898 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:56,249 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:56,600 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:56,951 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:57,301 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:57,652 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:58,004 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:58,355 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:58,706 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:59,057 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:59,408 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:20:59,758 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:00,109 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:00,460 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:00,811 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:01,162 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:01,513 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:01,864 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:02,214 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:02,565 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:02,916 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:03,267 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:03,617 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:03,968 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:04,319 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:04,670 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:05,020 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:05,371 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:05,722 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:06,072 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:06,423 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:06,774 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:07,125 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:07,476 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:07,827 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:08,177 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:08,528 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:08,879 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:09,229 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:09,580 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:09,931 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:10,282 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:10,632 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:10,983 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:11,334 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:11,685 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:12,036 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:12,386 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:12,737 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:13,088 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:13,438 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:13,789 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:14,140 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:14,491 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:14,843 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:15,194 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:15,544 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:15,895 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:16,246 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:16,596 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:16,947 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:17,297 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:17,648 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:17,999 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:18,349 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:18,700 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:19,051 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:19,403 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:19,753 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:20,104 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:20,455 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:20,806 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:21,156 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:21,507 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:21,858 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:22,209 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:22,560 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:22,910 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:23,261 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:23,612 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:23,963 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:24,314 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:24,664 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:25,015 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:25,366 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:25,717 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:26,068 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:26,419 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:26,769 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:27,120 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:27,471 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:27,822 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:28,173 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:28,523 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:28,874 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:29,225 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:29,576 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:29,927 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:30,278 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:30,629 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:30,980 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:31,330 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:31,681 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:32,032 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:32,383 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:32,734 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:33,085 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:33,435 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:33,786 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:34,137 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:34,488 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:34,889 - watchfiles.main - DEBUG - 78 changes detected: {(<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/hash_utils.cpython-311.pyc.140429463556800'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/__pycache__/magic_model.cpython-311.pyc.140429463552928'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/operators/__pycache__/pipes.cpython-311.pyc.140430185563392'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/__pycache__/magic_model.cpython-311.pyc.140429463552928'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/ocr_span_list_modify.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/post_proc/__pycache__/__init__.cpython-311.pyc.140429461241360'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/markdown_utils.cpython-311.pyc.140429461240304'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/config/__pycache__/model_block_type.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/operators/__pycache__/pipes.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/cut_image.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/dict2md/__pycache__/ocr_mkcontent.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/layoutlmv3/__pycache__/modeling_layoutlmv3.cpython-311.pyc.140430141909824'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/operators/__pycache__/models.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/__pycache__/pdf_parse_union_core_v2.cpython-311.pyc.140429467388592'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/layoutlmv3/__pycache__/modeling_layoutlmv3.cpython-311.pyc.140430141909824'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/post_proc/__pycache__/llm_aided.cpython-311.pyc.140429463555568'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/dict2md/__pycache__/ocr_mkcontent.cpython-311.pyc.140429461240832'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/__pycache__/pdf_parse_union_core_v2.cpython-311.pyc.140429467388592'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/construct_page_dict.cpython-311.pyc.140429467389552'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/config/__pycache__/make_content_config.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/post_proc/__pycache__/__init__.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/dict2md/__pycache__/ocr_mkcontent.cpython-311.pyc.140429461240832'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/__pycache__/pdf_parse_union_core_v2.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/version.cpython-311.pyc.140429463554864'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/construct_page_dict.cpython-311.pyc.140429467389552'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/ocr_span_list_modify.cpython-311.pyc.140429467390128'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/config/__pycache__/make_content_config.cpython-311.pyc.140429462268656'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/convert_utils.cpython-311.pyc.140429463557152'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/ocr_span_list_modify.cpython-311.pyc.140429467390128'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/config/__pycache__/make_content_config.cpython-311.pyc.140429462268656'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/coordinate_transform.cpython-311.pyc.140429467387824'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/convert_utils.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/hash_utils.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/post_proc/__pycache__/llm_aided.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/dict2md/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/remove_bbox_overlap.cpython-311.pyc.140429467388016'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/coordinate_transform.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/remove_bbox_overlap.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/remove_bbox_overlap.cpython-311.pyc.140429467388016'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/operators/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/cut_image.cpython-311.pyc.140429463556624'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/config/__pycache__/drop_tag.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/draw_bbox.cpython-311.pyc.140429463553280'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/json_compressor.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/cut_image.cpython-311.pyc.140429463556624'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/draw_bbox.cpython-311.pyc.140429463553280'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/post_proc/__pycache__/llm_aided.cpython-311.pyc.140429463555568'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/construct_page_dict.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/pdf_image_tools.cpython-311.pyc.140429463556272'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/__pycache__/magic_model.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/pdf_image_tools.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/config/__pycache__/drop_tag.cpython-311.pyc.140429463557328'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/config/__pycache__/model_block_type.cpython-311.pyc.140429467387632'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/version.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/post_proc/__pycache__/para_split_v3.cpython-311.pyc.140429467386480'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/version.cpython-311.pyc.140429463554864'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/pdf_image_tools.cpython-311.pyc.140429463556272'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/config/__pycache__/drop_tag.cpython-311.pyc.140429463557328'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/config/__pycache__/model_block_type.cpython-311.pyc.140429467387632'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/operators/__pycache__/models.cpython-311.pyc.140429463555040'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/post_proc/__pycache__/para_split_v3.cpython-311.pyc.140429467386480'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/convert_utils.cpython-311.pyc.140429463557152'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/coordinate_transform.cpython-311.pyc.140429467387824'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/operators/__pycache__/models.cpython-311.pyc.140429463555040'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/operators/__pycache__/pipes.cpython-311.pyc.140430185563392'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/markdown_utils.cpython-311.pyc.140429461240304'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/ocr_detect_all_bboxes.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/ocr_detect_all_bboxes.cpython-311.pyc.140429467389936'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/transformers/models/layoutlmv3/__pycache__/modeling_layoutlmv3.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/post_proc/__pycache__'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/post_proc/__pycache__/para_split_v3.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/json_compressor.cpython-311.pyc.140429463555392'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/draw_bbox.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/pre_proc/__pycache__/ocr_detect_all_bboxes.cpython-311.pyc.140429467389936'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/markdown_utils.cpython-311.pyc'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/json_compressor.cpython-311.pyc.140429463555392'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/libs/__pycache__/hash_utils.cpython-311.pyc.140429463556800'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:35,241 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:35,591 - watchfiles.main - DEBUG - 8 changes detected: {(<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/reading_oreder/layoutreader/__pycache__/helpers.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/reading_oreder/layoutreader/__pycache__'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/reading_oreder/__pycache__'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/reading_oreder/layoutreader/__pycache__/helpers.cpython-311.pyc.140429494184352'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/reading_oreder/layoutreader/__pycache__/__init__.cpython-311.pyc.140429494184144'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/reading_oreder/layoutreader/__pycache__/__init__.cpython-311.pyc'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.venv/lib64/python3.11/site-packages/magic_pdf/model/sub_modules/reading_oreder/layoutreader/__pycache__/helpers.cpython-311.pyc.140429494184352')}
2025-07-20 23:21:35,992 - watchfiles.main - DEBUG - 6 changes detected: {(<Change.added: 1>, '/home/<USER>/debug/limit-extract/.storage/convert/images/0393ebcaac05a05a0d4cfa55e01684b8c6991aeb2f9c468ba6e6d597434bdfaf.jpg'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.storage/convert/images/5f821b90e0438478bb02b6cb9869c4bcb335ab85729568a6919b6ffe2a84f524.jpg'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.storage/convert/images/ab4f4eeb4ada8170bb54d3bc223707c0a041f171493f99bca9472aa1596546fa.jpg'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.storage/convert/images/ca1be458a03a261eac7edac8a22c8293a1a6ccf58b2ba723dc5c1e2cb9c06d0b.jpg'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.storage/convert/images/d5779c95f7bb1eadf55ad7f83263804787f0a4b020947b20674c0fbcd7d42ed1.jpg')}
2025-07-20 23:21:36,343 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.added: 1>, '/home/<USER>/debug/limit-extract/.storage/convert/images/c544bd4e77c7e977ac1cdba63cef537ab6b0f3e08a52597b8a6b274bb6d9431b.jpg'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:36,693 - watchfiles.main - DEBUG - 5 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app_xxx.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.storage/convert/2899d831-4e87-4f8b-b60e-8b1002fd0597.md'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.storage/upload/2899d831-4e87-4f8b-b60e-8b1002fd0597.md')}
2025-07-20 23:21:37,044 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:37,396 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:37,746 - watchfiles.main - DEBUG - 3 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/limit_extract_workflow.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:38,097 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:38,447 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:38,798 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:39,148 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log')}
2025-07-20 23:21:39,499 - watchfiles.main - DEBUG - 3 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/limit_extract_workflow.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:39,900 - watchfiles.main - DEBUG - 10 changes detected: {(<Change.added: 1>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.3'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.2'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.2'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.3')}
2025-07-20 23:21:40,251 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:40,601 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:40,952 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:41,302 - watchfiles.main - DEBUG - 3 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/limit_extract_workflow.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:41,653 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:42,003 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:42,354 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:42,705 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:43,056 - watchfiles.main - DEBUG - 3 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/limit_extract_workflow.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:43,407 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:43,757 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:44,108 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:44,459 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:44,811 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:45,161 - watchfiles.main - DEBUG - 3 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/limit_extract_workflow.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:45,512 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:45,864 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:46,216 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:46,567 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:46,918 - watchfiles.main - DEBUG - 4 changes detected: {(<Change.added: 1>, '/home/<USER>/debug/limit-extract/output/limit_extract_results_20250720_232146.xlsx'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/limit_extract_workflow.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:47,268 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:47,619 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:47,969 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:48,320 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:48,670 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:49,021 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:49,371 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:49,722 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:50,073 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:50,423 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:50,774 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:51,125 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:51,476 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:51,827 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:52,178 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:52,529 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:52,879 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:53,230 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:53,581 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:53,932 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:54,282 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:54,633 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:54,984 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:55,334 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:55,685 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:56,036 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:56,387 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:56,738 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:57,088 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:57,439 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:57,789 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:58,140 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:58,491 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:58,841 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:59,192 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:59,543 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:21:59,894 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:00,245 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:00,596 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:00,947 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:01,298 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:01,649 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:02,000 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:02,350 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:02,701 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:03,051 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:03,402 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:03,752 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:04,103 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:04,453 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:04,804 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:05,154 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:05,505 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:05,855 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:06,206 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:06,556 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/test/annotated-test.ipynb'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:06,908 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:07,258 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:07,609 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:07,959 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:08,310 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:08,660 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:09,011 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:09,361 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:09,712 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:10,062 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:10,413 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:10,764 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:11,114 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:11,465 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:11,815 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:12,166 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:12,516 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:12,868 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:13,219 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:13,570 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:13,921 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:14,271 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:14,622 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:14,973 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:15,323 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:15,674 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:16,025 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:16,375 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:16,726 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:17,077 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:17,427 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:17,778 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:18,129 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:18,479 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:18,830 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:19,180 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:19,531 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:19,881 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:20,232 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:20,583 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:20,934 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:21,284 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:21,635 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:21,985 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:22,336 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:22,686 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:23,037 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:23,387 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:23,738 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:24,088 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:24,439 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:24,790 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:25,140 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:25,491 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:25,841 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:26,192 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:26,542 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:26,893 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:27,244 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:27,594 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:27,945 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:28,295 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:28,646 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:28,996 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:29,347 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:29,697 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:30,048 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:30,398 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:30,749 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:31,100 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:31,450 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:31,801 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:32,151 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:32,502 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:32,852 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:33,203 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:33,554 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:33,904 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:34,255 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:34,605 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:34,956 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:35,307 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:35,657 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:36,008 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:36,358 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:36,709 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:37,060 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:37,410 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:37,761 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:38,111 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:38,462 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:38,813 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:39,163 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:39,514 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:39,866 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:40,216 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:40,567 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:40,918 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:41,269 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:41,619 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:41,970 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:42,320 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:42,672 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:43,022 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:43,373 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:43,723 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:44,074 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:44,425 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:44,776 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:45,127 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:45,478 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:45,829 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:46,180 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:46,530 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:46,881 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:47,231 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:47,582 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:47,932 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:48,283 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:48,633 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:48,984 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:49,335 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:49,685 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:50,036 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:50,387 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:50,737 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:51,088 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:51,438 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:51,789 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:52,140 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:52,491 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:52,841 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:53,192 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:53,542 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:53,893 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:54,244 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:54,594 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:54,945 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:55,295 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:55,646 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:55,997 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:56,348 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:56,699 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:57,050 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:57,400 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:57,751 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:58,102 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:58,452 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:58,803 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:59,153 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:59,504 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:22:59,855 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:00,205 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:00,556 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:00,906 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:01,257 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:01,607 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:01,958 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:02,308 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:02,659 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:03,009 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:03,360 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:03,712 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:04,063 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:04,413 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:04,814 - watchfiles.main - DEBUG - 4 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.git/FETCH_HEAD'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/.git/objects/maintenance.lock'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/.git/objects/maintenance.lock'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:05,165 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:05,515 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:05,866 - watchfiles.main - DEBUG - 2 changes detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/MinerU/.git/FETCH_HEAD'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:06,216 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:06,567 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:06,918 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:07,269 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:07,620 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:07,971 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:08,321 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:08,672 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:09,023 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:09,373 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:09,724 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:10,075 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:10,425 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:10,776 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:11,127 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:11,477 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:11,828 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:12,179 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:12,529 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:12,880 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:13,231 - watchfiles.main - DEBUG - 6 changes detected: {(<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/MinerU/.git/objects/maintenance.lock'), (<Change.deleted: 3>, '/home/<USER>/debug/limit-extract/MinerU/.git/refs/remotes/origin/HEAD.lock'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/MinerU/.git/FETCH_HEAD'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/MinerU/.git/objects/maintenance.lock'), (<Change.added: 1>, '/home/<USER>/debug/limit-extract/MinerU/.git/refs/remotes/origin/HEAD.lock'), (<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:13,581 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:13,932 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:14,283 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:14,633 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:14,984 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:15,335 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:15,685 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:16,036 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:16,387 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:16,737 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:17,088 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:17,439 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:17,790 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:18,140 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:18,491 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:18,842 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:19,194 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:19,544 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:19,895 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:20,245 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:20,596 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:20,946 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:21,297 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:21,648 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:21,999 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:22,350 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:22,701 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:23,053 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:23,404 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:23,755 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:24,106 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:24,457 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:24,808 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:25,159 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:25,509 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:25,861 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:26,212 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:26,563 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:26,914 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:27,264 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:27,615 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:27,966 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:28,317 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:28,668 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:29,019 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:29,369 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:29,720 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:30,071 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:30,422 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:30,774 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:31,125 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:31,477 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:31,827 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:32,178 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:32,529 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:32,881 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:33,231 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:33,583 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:33,934 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:34,284 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:34,635 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:34,986 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:35,337 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:35,687 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:36,038 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:36,388 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:36,740 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:37,092 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:37,442 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:37,793 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:38,143 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:38,494 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:38,845 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:39,197 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:39,548 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:39,900 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:40,251 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:40,602 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:40,952 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:41,303 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:41,654 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:42,004 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:42,355 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:42,706 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:43,057 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:43,408 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:43,758 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:44,109 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:44,459 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:44,810 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:45,160 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:45,511 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:45,861 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:46,212 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:46,563 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:46,914 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:47,265 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:47,615 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:47,966 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:48,317 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:48,668 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:49,019 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:49,371 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:49,722 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:50,074 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:50,424 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:50,775 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:51,127 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:51,478 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:51,828 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:52,179 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:52,529 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:52,880 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:53,231 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:53,581 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:53,932 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:54,282 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:54,633 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:54,984 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:55,334 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:55,685 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:56,035 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:56,386 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:56,737 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:57,087 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:57,438 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:57,789 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:58,139 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:58,490 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:58,840 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:59,191 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:59,542 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:23:59,893 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:00,244 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:00,595 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:00,945 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:01,297 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:01,647 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:01,998 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:02,349 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:02,700 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:03,050 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:03,401 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:03,752 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:04,103 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:04,454 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:04,806 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:05,158 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:05,509 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:05,860 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:06,211 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:06,562 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:06,913 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:07,264 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:07,615 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:07,965 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:08,316 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:08,667 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:09,018 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:09,368 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:09,719 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:10,071 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:10,421 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:10,772 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:11,123 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:11,474 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:11,825 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:12,176 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:12,527 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:12,877 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:13,228 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:13,578 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:13,929 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:14,280 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:14,630 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
2025-07-20 23:24:14,981 - watchfiles.main - DEBUG - 1 change detected: {(<Change.modified: 2>, '/home/<USER>/debug/limit-extract/.logs/my_app.log.1')}
