"""
数据库模型定义
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Index, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import enum

Base = declarative_base()


class FileType(enum.Enum):
    """文件类型枚举"""
    PDF = "pdf"
    IMAGE = "image"
    EXCEL = "excel"
    OTHER = "other"


class WorkflowStatus(enum.Enum):
    """工作流状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(100), unique=True, nullable=False, index=True)  # 业务用户ID
    username = Column(String(100), nullable=True)
    email = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # 关系
    files = relationship("File", back_populates="user")
    workflows = relationship("Workflow", back_populates="user")


class File(Base):
    """文件表"""
    __tablename__ = "files"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(100), ForeignKey("users.user_id"), nullable=False, index=True)
    
    # 文件基本信息
    original_filename = Column(String(255), nullable=False)  # 原始文件名
    stored_filename = Column(String(255), nullable=False)    # 存储文件名(UUID)
    file_path = Column(String(500), nullable=False)          # 完整文件路径
    file_size = Column(BigInteger, nullable=False)           # 文件大小(字节)
    file_type = Column(String(20), nullable=False)           # 文件类型
    mime_type = Column(String(100), nullable=True)           # MIME类型
    
    # 文件哈希和去重
    file_hash = Column(String(64), nullable=False, index=True)  # SHA256哈希
    md5_hash = Column(String(32), nullable=True, index=True)    # MD5哈希(备用)
    
    # 元数据
    upload_ip = Column(String(45), nullable=True)            # 上传IP
    user_agent = Column(Text, nullable=True)                 # 用户代理
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # 状态
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime, nullable=True)
    
    # 关系
    user = relationship("User", back_populates="files")
    workflow_files = relationship("WorkflowFile", back_populates="file")
    
    # 索引
    __table_args__ = (
        Index('idx_file_hash_user', 'file_hash', 'user_id'),
        Index('idx_created_at', 'created_at'),
    )


class Workflow(Base):
    """工作流执行记录表"""
    __tablename__ = "workflows"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    workflow_id = Column(String(36), unique=True, nullable=False, index=True)  # UUID
    user_id = Column(String(100), ForeignKey("users.user_id"), nullable=False, index=True)
    
    # 工作流配置
    workflow_type = Column(String(50), nullable=False, default="limit_extract")
    queries = Column(Text, nullable=False)  # JSON格式存储查询列表
    timeout_seconds = Column(Integer, nullable=False, default=60)
    
    # 执行状态
    status = Column(String(20), nullable=False, default="pending")
    progress_percentage = Column(Integer, default=0, nullable=False)
    
    # 时间信息
    created_at = Column(DateTime, default=func.now(), nullable=False)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # 结果信息
    result_data = Column(Text, nullable=True)  # JSON格式存储结果
    error_message = Column(Text, nullable=True)
    error_details = Column(Text, nullable=True)  # JSON格式存储错误详情
    
    # 输出文件
    output_excel_path = Column(String(500), nullable=True)
    
    # 关系
    user = relationship("User", back_populates="workflows")
    workflow_files = relationship("WorkflowFile", back_populates="workflow")
    workflow_logs = relationship("WorkflowLog", back_populates="workflow")
    
    # 索引
    __table_args__ = (
        Index('idx_status_created', 'status', 'created_at'),
        Index('idx_user_created', 'user_id', 'created_at'),
    )


class WorkflowFile(Base):
    """工作流文件关联表"""
    __tablename__ = "workflow_files"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    workflow_id = Column(String(36), ForeignKey("workflows.workflow_id"), nullable=False)
    file_id = Column(Integer, ForeignKey("files.id"), nullable=False)
    
    # 文件在工作流中的角色
    file_role = Column(String(20), nullable=False)  # 'pdf_document', 'nutrition_image', 'output_excel'
    
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # 关系
    workflow = relationship("Workflow", back_populates="workflow_files")
    file = relationship("File", back_populates="workflow_files")
    
    # 索引
    __table_args__ = (
        Index('idx_workflow_file', 'workflow_id', 'file_id'),
        Index('idx_workflow_role', 'workflow_id', 'file_role'),
    )


class WorkflowLog(Base):
    """工作流执行日志表"""
    __tablename__ = "workflow_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    workflow_id = Column(String(36), ForeignKey("workflows.workflow_id"), nullable=False, index=True)
    
    # 日志信息
    log_level = Column(String(10), nullable=False)  # INFO, WARNING, ERROR
    step_name = Column(String(100), nullable=True)  # 工作流步骤名称
    message = Column(Text, nullable=False)
    details = Column(Text, nullable=True)  # JSON格式存储详细信息
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # 关系
    workflow = relationship("Workflow", back_populates="workflow_logs")
    
    # 索引
    __table_args__ = (
        Index('idx_workflow_created', 'workflow_id', 'created_at'),
    )


class FileDeduplication(Base):
    """文件去重表"""
    __tablename__ = "file_deduplication"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    file_hash = Column(String(64), nullable=False, index=True)
    original_file_id = Column(Integer, ForeignKey("files.id"), nullable=False)  # 原始文件ID
    duplicate_file_id = Column(Integer, ForeignKey("files.id"), nullable=False)  # 重复文件ID
    
    detected_at = Column(DateTime, default=func.now(), nullable=False)
    
    # 索引
    __table_args__ = (
        Index('idx_hash_original', 'file_hash', 'original_file_id'),
    )


class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = "system_config"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    config_key = Column(String(100), unique=True, nullable=False)
    config_value = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
