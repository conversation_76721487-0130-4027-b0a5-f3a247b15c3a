import logging
from typing import Callable
from pathlib import Path
import logging.config
from functools import wraps


LOG_DIR = ".logs"
logger = logging.getLogger(__name__)

logging_config = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {},
    "formatters": {
        "simple": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"  # 回头整点好看的format
        },
    },
    "handlers": {
        "stderr": {
            "class": "logging.StreamHandler",
            "formatter": "simple",
            "level": "DEBUG",
            "stream": "ext://sys.stderr",
        },
        "file_root": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "DEBUG",
            "formatter": "simple",
            "filename": str(Path(LOG_DIR) / "my_app.log"),
            "maxBytes": 1024 * 1024 * 5,
            "backupCount": 3,
        },
        "file_proc_file": {  # 某部分专门设置一个handler
            "class": "logging.handlers.RotatingFileHandler",
            "level": "DEBUG",
            "formatter": "simple",
            "filename": str(Path(LOG_DIR) / "my_app_xxx.log"),
            "maxBytes": 1024 * 1024 * 5,
            "backupCount": 3,
        },
        "limit_extract_workflow": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "DEBUG",
            "formatter": "simple",
            "filename": str(Path(LOG_DIR) / "limit_extract_workflow.log"),
            "maxBytes": 1024 * 1024 * 5,
            "backupCount": 3,
        },
    },
    "loggers": {
        "root": {
            "level": "DEBUG",
            "handlers": ["file_root"],
        },  # roothandler由于会收集第三方pack的日志, 所以就不输出到stderr了,输出到文件作个备份把.
        "proc_file": {
            "level": "DEBUG",
            "handlers": ["file_proc_file", "stderr"],
            "propagate": False,
        },  # 共用这个logger的所有代码 输出到一个文件.
        "limit_extract_workflow": {
            "level": "DEBUG",
            "handlers": ["limit_extract_workflow", "stderr"],
            "propagate": False,
        },
    },
}

_initialized = False


def setup_logging():
    global _initialized
    if _initialized:
        return
    log_path = Path(LOG_DIR)
    log_path.mkdir(parents=True, exist_ok=True)
    logging.config.dictConfig(logging_config)
    _initialized = True


def trace_func(func: Callable):
    def wrapper(*args, **kargs):
        logger.info(f"executing {func.__name__}")
        result = func(*args, **kargs)
        return result

    return wrapper


def trace_func(logger: logging.Logger | None = None):
    """
    带参数的装饰器，允许指定 logger。
    如果没有指定，则默认使用根 logger。
    """
    if logger is None:
        logger = logging.getLogger()  # 默认是 root logger

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger.info(f"Executing function: {func.__name__}")
            result = func(*args, **kwargs)
            return result

        return wrapper

    return decorator


setup_logging()


def get_logger(name: str):
    return logging.getLogger(name)
