<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>限值提取工作流API - 控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 30px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .header h1 {
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }
        
        .header .subtitle {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 80px);
            gap: 0;
        }
        
        .left-panel {
            width: 45%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            overflow-y: auto;
            border-right: 1px solid rgba(0,0,0,0.1);
        }
        
        .right-panel {
            width: 55%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 30px;
            overflow-y: auto;
            color: white;
        }
        
        .section {
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .section h2 {
            color: #333;
            font-size: 18px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section h2::before {
            content: '';
            width: 4px;
            height: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 2px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .upload-result {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            font-size: 13px;
        }
        
        .upload-result.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: rgba(244, 67, 54, 0.3);
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.success {
            background: #4caf50;
        }
        
        .status-indicator.error {
            background: #f44336;
        }
        
        .status-indicator.pending {
            background: #ff9800;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.9);
            border-radius: 12px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.4;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .log-container::-webkit-scrollbar {
            width: 8px;
        }
        
        .log-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        .log-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 6px 10px;
            border-radius: 4px;
            border-left: 3px solid transparent;
        }
        
        .log-entry.info {
            color: #81c784;
            border-left-color: #81c784;
            background: rgba(129, 199, 132, 0.1);
        }
        
        .log-entry.success {
            color: #4caf50;
            border-left-color: #4caf50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .log-entry.error {
            color: #f44336;
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .log-entry.warning {
            color: #ff9800;
            border-left-color: #ff9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .log-header h2 {
            color: white;
            font-size: 18px;
            margin: 0;
        }
        
        .clear-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .clear-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .progress-section {
            margin-bottom: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4caf50, #81c784);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #81c784;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔬 限值提取工作流控制台</h1>
        <div class="subtitle">基于LlamaIndex Workflow的智能文档分析系统</div>
    </div>
    
    <div class="main-container">
        <!-- 左侧控制面板 -->
        <div class="left-panel">
            <!-- 用户信息 -->
            <div class="section">
                <h2>👤 用户信息</h2>
                <div class="form-group">
                    <label for="userId">用户ID</label>
                    <input type="text" id="userId" value="user_demo" placeholder="输入您的用户ID">
                </div>
            </div>
            
            <!-- 文件上传 -->
            <div class="section">
                <h2>📁 文件上传</h2>
                <div class="form-group">
                    <label for="pdfFile">PDF文档</label>
                    <input type="file" id="pdfFile" accept=".pdf">
                    <div id="pdfUploadResult" class="upload-result" style="display: none;"></div>
                </div>
                
                <div class="form-group">
                    <label for="imageFile">营养成分表图片</label>
                    <input type="file" id="imageFile" accept=".png,.jpg,.jpeg,.gif,.bmp">
                    <div id="imageUploadResult" class="upload-result" style="display: none;"></div>
                </div>
                
                <button class="btn" onclick="uploadFiles()">
                    <span class="status-indicator pending" id="uploadStatus" style="display: none;"></span>
                    上传文件
                </button>
            </div>
            
            <!-- 查询配置 -->
            <div class="section">
                <h2>🔍 查询配置</h2>
                <div class="form-group">
                    <label for="queries">分析查询 (每行一个)</label>
                    <textarea id="queries" rows="6" placeholder="请输入要分析的查询项目...">铅的限量标准是多少？
微生物指标的要求是什么？
产品的保质期是多长时间？
贮存条件有什么要求？
感官要求包括哪些项目？</textarea>
                </div>
                
                <div class="form-group">
                    <label for="timeout">超时时间 (秒)</label>
                    <input type="number" id="timeout" value="120" min="30" max="300">
                </div>
                
                <button class="btn" onclick="startExtraction()" id="extractBtn">
                    <span class="status-indicator pending" id="extractStatus" style="display: none;"></span>
                    开始分析
                </button>
            </div>
        </div>
        
        <!-- 右侧结果面板 -->
        <div class="right-panel">
            <!-- 执行状态 -->
            <div class="progress-section">
                <div class="log-header">
                    <h2>📊 执行状态</h2>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalQueries">0</div>
                        <div class="stat-label">查询总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="executionTime">0s</div>
                        <div class="stat-label">执行时间</div>
                    </div>
                </div>
            </div>
            
            <!-- 实时日志 -->
            <div class="log-header">
                <h2>📝 实时日志</h2>
                <button class="clear-btn" onclick="clearLog()">清空日志</button>
            </div>
            <div class="log-container" id="logContainer"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let pdfFilename = null;
        let imageFilename = null;
        let startTime = null;
        let executionTimer = null;

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<span style="opacity: 0.7;">[${timestamp}]</span> ${message}`;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }

        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        function updateExecutionTime() {
            if (startTime) {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                document.getElementById('executionTime').textContent = elapsed + 's';
            }
        }

        function setButtonStatus(buttonId, statusId, loading, text) {
            const button = document.getElementById(buttonId);
            const status = document.getElementById(statusId);

            button.disabled = loading;
            button.innerHTML = loading ?
                `<span class="status-indicator pending"></span>${text}` :
                text;
        }

        async function uploadFile(file, userId, resultElementId) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('user_id', userId);

            try {
                const response = await fetch(`${API_BASE}/upload`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                const resultElement = document.getElementById(resultElementId);

                if (response.ok) {
                    resultElement.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span class="status-indicator success"></span>
                            <div>
                                <strong>上传成功!</strong><br>
                                <small>文件: ${result.filename} (${(result.file_size / 1024).toFixed(1)} KB)</small>
                            </div>
                        </div>
                    `;
                    resultElement.className = 'upload-result';
                    resultElement.style.display = 'block';
                    log(`✅ 文件上传成功: ${result.filename}`, 'success');
                    return result.filename;
                } else {
                    resultElement.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span class="status-indicator error"></span>
                            <div><strong>上传失败:</strong> ${result.detail}</div>
                        </div>
                    `;
                    resultElement.className = 'upload-result error';
                    resultElement.style.display = 'block';
                    log(`❌ 文件上传失败: ${result.detail}`, 'error');
                    return null;
                }
            } catch (error) {
                log(`❌ 上传请求失败: ${error.message}`, 'error');
                return null;
            }
        }

        async function uploadFiles() {
            const userId = document.getElementById('userId').value;
            const pdfFile = document.getElementById('pdfFile').files[0];
            const imageFile = document.getElementById('imageFile').files[0];

            if (!userId.trim()) {
                alert('请输入用户ID');
                return;
            }

            if (!pdfFile || !imageFile) {
                alert('请选择PDF文件和图片文件');
                return;
            }

            log('🚀 开始上传文件...', 'info');
            updateProgress(10);

            // 上传PDF文件
            pdfFilename = await uploadFile(pdfFile, userId, 'pdfUploadResult');
            updateProgress(50);

            // 上传图片文件
            imageFilename = await uploadFile(imageFile, userId, 'imageUploadResult');
            updateProgress(100);

            if (pdfFilename && imageFilename) {
                log('🎉 所有文件上传完成，可以开始分析', 'success');
            }
        }

        async function startExtraction() {
            if (!pdfFilename || !imageFilename) {
                alert('请先上传PDF文件和图片文件');
                return;
            }

            const userId = document.getElementById('userId').value;
            const queriesText = document.getElementById('queries').value;
            const timeout = parseInt(document.getElementById('timeout').value);

            const queries = queriesText.split('\n').filter(q => q.trim()).map(q => q.trim());

            if (queries.length === 0) {
                alert('请输入至少一个查询');
                return;
            }

            document.getElementById('totalQueries').textContent = queries.length;

            const requestData = {
                user_id: userId,
                queries: queries,
                pdf_filename: pdfFilename,
                image_filename: imageFilename,
                timeout: timeout
            };

            log('🔄 开始限值提取分析...', 'info');
            log(`📋 查询项目: ${queries.length} 个`, 'info');

            const extractBtn = document.getElementById('extractBtn');
            extractBtn.disabled = true;
            extractBtn.textContent = '分析中...';

            startTime = Date.now();
            executionTimer = setInterval(updateExecutionTime, 1000);
            updateProgress(0);

            try {
                const response = await fetch(`${API_BASE}/extract`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let progress = 0;

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const dataStr = line.slice(6);
                            if (dataStr.trim()) {
                                try {
                                    const eventData = JSON.parse(dataStr);
                                    const eventType = eventData.type || 'unknown';
                                    const message = eventData.message || '';

                                    let logType = 'info';
                                    let icon = '📝';

                                    if (eventType === 'error') {
                                        logType = 'error';
                                        icon = '❌';
                                    } else if (eventType === 'success') {
                                        logType = 'success';
                                        icon = '✅';
                                        progress = 100;
                                    } else if (eventType === 'workflow_event') {
                                        logType = 'warning';
                                        icon = '⚙️';
                                        progress = Math.min(progress + 15, 90);
                                    } else if (eventType === 'progress') {
                                        icon = '🔄';
                                        progress = Math.min(progress + 10, 80);
                                    }

                                    updateProgress(progress);
                                    log(`${icon} ${message}`, logType);

                                    if (eventType === 'success') {
                                        const result = eventData.result || {};
                                        if (result.excel_file) {
                                            log(`📊 Excel文件已生成: ${result.excel_file}`, 'success');
                                        }
                                        if (result.metadata) {
                                            const metadata = result.metadata;
                                            log(`📈 处理统计: 查询${metadata.total_queries}个, Excel生成${metadata.excel_generated ? '成功' : '失败'}`, 'info');
                                        }
                                    }

                                    if (eventType === 'end') {
                                        log('🏁 分析完成', 'success');
                                        break;
                                    }
                                } catch (e) {
                                    log(`⚠️ 解析事件数据失败: ${dataStr}`, 'error');
                                }
                            }
                        }
                    }
                }

            } catch (error) {
                log(`❌ 执行失败: ${error.message}`, 'error');
                updateProgress(0);
            } finally {
                extractBtn.disabled = false;
                extractBtn.textContent = '开始分析';
                if (executionTimer) {
                    clearInterval(executionTimer);
                    executionTimer = null;
                }
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🌟 限值提取工作流控制台已启动', 'success');
            log('💡 请先上传PDF文档和营养成分表图片', 'info');
        });
    </script>
</body>
</html>
