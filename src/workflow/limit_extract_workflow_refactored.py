"""
重构后的限值提取工作流 - 职责分离版本
"""

import asyncio
from llama_index.core.workflow import StartEvent, StopEvent, Workflow, Context, step

from .events import (
    WorkflowInputEvent,
    DocParseEvent,
    DocSearchEvent,
    NutritionParseEvent,
    MergeEvent,
    ComposeResultEvent,
)
from ..data.models import WorkflowInput, WorkflowResult, TableData
from ..data.processors import DocumentAnalysisProcessor, NutritionDataProcessor
from ..output.excel_generator import ExcelGenerator
from ..proc_file.proc_file import FileProcessor
from ..proc_file.indexer import indexer, QueryContent
from ..config.config import app_config
from ..utils.constants import StatusConstants
from ..log.loggers import get_logger

logger = get_logger("limit_extract_workflow")


class LimitExtractWorkflowRefactored(Workflow):
    """重构后的限值提取工作流"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.config = app_config
        self.file_processor = FileProcessor()
        self.doc_processor = DocumentAnalysisProcessor()
        self.nutrition_processor = NutritionDataProcessor()
        self.excel_generator = ExcelGenerator()

    @step
    async def dispatch_query(self, ctx: Context, ev: StartEvent) -> None:
        """分发查询到两个并行分支"""
        # 从StartEvent中获取数据
        workflow_data = ev.data if hasattr(ev, "data") and ev.data else {}

        # 使用配置的默认值
        file_path = workflow_data.get("file_path", self.config.paths.default_pdf)
        image_path = workflow_data.get("image_path", self.config.paths.default_image)
        user_id = workflow_data.get("user_id", self.config.workflow.default_user_id)
        queries = workflow_data.get("queries", self.config.default_queries)

        logger.info(f"开始处理文件: {file_path}, 图片: {image_path}")
        logger.info(f"查询项目: {queries}")

        # 发送文档解析事件
        ctx.send_event(
            DocParseEvent(file_path=file_path, user_id=user_id, queries=queries)
        )

        # 发送营养成分表解析事件
        ctx.send_event(NutritionParseEvent(image_path=image_path))

    @step
    async def parse_doc(self, ctx: Context, ev: DocParseEvent) -> DocSearchEvent:
        """解析文档并建立索引"""
        try:
            logger.info(f"开始解析文档: {ev.file_path}")
            success = self.file_processor.proc_file(ev.file_path, ev.user_id)

            if not success:
                raise ValueError(f"文档处理失败: {ev.file_path}")

            logger.info("文档解析完成，开始搜索")
            return DocSearchEvent(queries=ev.queries, user_id=ev.user_id)

        except Exception as e:
            logger.error(f"文档解析失败: {e}")
            return DocSearchEvent(queries=[], user_id=ev.user_id)

    @step
    async def map_reduce_search(self, ctx: Context, ev: DocSearchEvent) -> MergeEvent:
        """在索引中搜索相关信息"""
        try:
            if not ev.queries:
                logger.warning("查询列表为空，返回空结果")
                return MergeEvent(source="doc_search", data={"results": {}})

            logger.info(f"开始执行 {len(ev.queries)} 个查询")
            results = {}

            for i, query in enumerate(ev.queries):
                try:
                    logger.info(f"执行查询 {i + 1}/{len(ev.queries)}: {query}")
                    query_content = QueryContent(query=query, user_id=ev.user_id)
                    result = indexer.query(query_content)
                    results[query] = result
                except Exception as e:
                    logger.error(f"查询失败 '{query}': {e}")
                    results[query] = f"查询失败: {str(e)}"

            logger.info("文档搜索完成")
            return MergeEvent(source="doc_search", data={"results": results})

        except Exception as e:
            logger.error(f"文档搜索失败: {e}")
            return MergeEvent(source="doc_search", data={"error": str(e)})

    @step
    async def extract_nutrition_table(
        self, ctx: Context, ev: NutritionParseEvent
    ) -> MergeEvent:
        """提取营养成分表"""
        try:
            logger.info(f"开始提取营养成分表: {ev.image_path}")

            # 使用数据处理器提取营养成分
            nutrition_result = self.nutrition_processor.extract_from_image(
                ev.image_path
            )

            logger.info("营养成分表提取完成")
            return MergeEvent(source="nutrition_table", data=nutrition_result.to_dict())

        except Exception as e:
            logger.error(f"营养成分表提取失败: {e}")
            return MergeEvent(
                source="nutrition_table",
                data={"result": f"提取失败: {str(e)}", "success": False},
            )

    @step
    async def collect(self, ctx: Context, ev: MergeEvent) -> ComposeResultEvent | None:
        """收集两个分支的结果"""
        result = ctx.collect_events(ev, [MergeEvent] * 2)
        if result is None:
            return None

        # 分离文档搜索结果和营养成分表结果
        doc_data = {}
        nutrition_data = {}

        for event in result:
            if event.source == "doc_search":
                doc_data = event.data
            elif event.source == "nutrition_table":
                nutrition_data = event.data

        # 使用数据处理器处理结果
        doc_result = self.doc_processor.process_query_results(
            doc_data.get("results", {})
        )

        # 重构营养成分结果
        if nutrition_data.get("success", False) and "result" in nutrition_data:
            from ..data.models import NutritionTable, NutritionExtractionResult

            if isinstance(nutrition_data["result"], dict):
                nutrition_table = NutritionTable.from_dict(nutrition_data["result"])
                nutrition_result = NutritionExtractionResult(nutrition_table, True)
            else:
                nutrition_result = NutritionExtractionResult(
                    None, False, str(nutrition_data["result"])
                )
        else:
            nutrition_result = NutritionExtractionResult(
                None, False, nutrition_data.get("result", "未知错误")
            )

        logger.info("结果收集完成")
        return ComposeResultEvent(
            doc_result=doc_result, nutrition_result=nutrition_result
        )

    @step
    async def compose_table(self, ctx: Context, ev: ComposeResultEvent) -> StopEvent:
        """组合最终结果表格"""
        try:
            logger.info("开始组合最终结果表格")

            # 创建表格数据
            doc_table = self.doc_processor.create_table_data(ev.doc_result)
            nutrition_table = self.nutrition_processor.create_table_data(
                ev.nutrition_result
            )
            summary_table = self._create_summary_table(
                ev.doc_result, ev.nutrition_result
            )

            # 生成Excel文件
            excel_file_path = self.excel_generator.generate_excel(
                doc_table, nutrition_table, summary_table
            )

            # 构建工作流结果
            workflow_result = WorkflowResult(
                document_analysis=ev.doc_result,
                nutrition_extraction=ev.nutrition_result,
                excel_file_path=excel_file_path,
                processing_time=asyncio.get_event_loop().time(),
            )

            logger.info("结果表格组合完成")
            return StopEvent(result=workflow_result)

        except Exception as e:
            logger.error(f"结果组合失败: {e}")
            return StopEvent(result={"error": str(e)})

    def _create_summary_table(self, doc_result, nutrition_result) -> TableData:
        """创建执行摘要表格"""
        headers = ["项目", "状态", "说明"]
        rows = [
            [
                "文档分析",
                f"{StatusConstants.SUCCESS_SYMBOL} {StatusConstants.Messages.SUCCESS}"
                if doc_result.success
                else f"{StatusConstants.FAILURE_SYMBOL} {StatusConstants.Messages.FAILURE}",
                "多查询文档信息提取",
            ],
            [
                "营养成分表提取",
                f"{StatusConstants.SUCCESS_SYMBOL} {StatusConstants.Messages.SUCCESS}"
                if nutrition_result.success
                else f"{StatusConstants.FAILURE_SYMBOL} {StatusConstants.Messages.FAILURE}",
                "图片OCR识别",
            ],
            ["执行时间", f"{asyncio.get_event_loop().time():.2f}秒", "总处理时间"],
            [
                "整体状态",
                f"{StatusConstants.SUCCESS_SYMBOL} {StatusConstants.Messages.COMPLETED}"
                if doc_result.success and nutrition_result.success
                else f"{StatusConstants.WARNING_SYMBOL} {StatusConstants.Messages.PARTIAL_COMPLETED}",
                "工作流执行状态",
            ],
        ]

        return TableData(
            table_type="execution_summary", title="执行摘要", headers=headers, rows=rows
        )
